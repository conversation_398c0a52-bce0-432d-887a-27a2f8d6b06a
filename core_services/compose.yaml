networks:
  kafka-network:
    name: kafka-network # shared network for Kafka-related services

services:
  # Runs the PostgreSQL database with TimescaleDB
  postgres:
    build:
      context: .
      dockerfile: ./postgres/Dockerfile
    container_name: postgresql
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_MULTIPLE_USERS=${POSTGRES_USER_WLSAPI},${POSTGRES_USER_GAME_AGGREGATOR}
      - POSTGRES_PASSWORD_WLSAPI=${POSTGRES_PASSWORD_WLSAPI}
      - POSTGRES_PASSWORD_GAME_AGGREGATOR=${POSTGRES_PASSWORD_GAME_AGGREGATOR}
    ports:
      - '${POSTGRES_PORT}:5432'
    command: ['postgres', '-c', 'shared_preload_libraries=timescaledb']
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 30s
      timeout: 30s
      retries: 3

  # Runs the MongoDB cluster database
  ## Router Servers
  mongo-router:
    build:
      context: mongo
    container_name: mongo-router
    ports:
      - ${MONGO_PORT}:27017
    depends_on:
      mongo-configsvr:
        condition: service_healthy
      mongo-shard1:
        condition: service_healthy
      mongo-shard2:
        condition: service_healthy
    env_file:
      - .env
    entrypoint: ['/scripts/entrypoint-router.sh']
    volumes:
      - ./mongo/scripts:/scripts
      - mongodb_cluster_router_db:/data/db
      - mongodb_cluster_router_config:/data/configdb
    healthcheck:
      test:
        [
          'CMD',
          'mongosh',
          '--quiet',
          '127.0.0.1/test',
          '--eval',
          "'quit(db.runCommand({ ping: 1 }).ok ? 0 : 2)'",
        ]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s
    logging:
      driver: 'json-file'
      options:
        max-size: '200k'
        max-file: '10'

  ## Config Servers
  mongo-configsvr:
    build:
      context: mongo
    container_name: mongo-configsvr
    volumes:
      - ./mongo/scripts:/scripts
      - mongodb_cluster_configsvr_db:/data/db
      - mongodb_cluster_configsvr_config:/data/configdb
    ports:
      - 27117:27017
    env_file:
      - .env
    entrypoint: ['/scripts/entrypoint-configsvr.sh']
    healthcheck:
      test:
        [
          'CMD',
          'mongosh',
          '--quiet',
          '127.0.0.1/test',
          '--eval',
          "'quit(db.runCommand({ ping: 1 }).ok ? 0 : 2)'",
        ]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s
    logging:
      driver: 'json-file'
      options:
        max-size: '200k'
        max-file: '10'

  ## Shards Servers
  mongo-shard1:
    build:
      context: mongo
    container_name: mongo-shard1
    volumes:
      - ./mongo/scripts:/scripts
      - mongodb_cluster_shard1_db:/data/db
      - mongodb_cluster_shard1_config:/data/configdb
    ports:
      - 27118:27017
    env_file:
      - .env
    entrypoint: ['/bin/sh', '/scripts/entrypoint-shard1.sh']
    healthcheck:
      test:
        [
          'CMD',
          'mongosh',
          '--quiet',
          '127.0.0.1/test',
          '--eval',
          "'quit(db.runCommand({ ping: 1 }).ok ? 0 : 2)'",
        ]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s
    logging:
      driver: 'json-file'
      options:
        max-size: '200k'
        max-file: '10'

  ## Shards 02
  mongo-shard2:
    build:
      context: mongo
    container_name: mongo-shard2
    volumes:
      - ./mongo/scripts:/scripts
      - mongodb_cluster_shard2_db:/data/db
      - mongodb_cluster_shard2_config:/data/configdb
    ports:
      - 27119:27017
    env_file:
      - .env
    entrypoint: ['/bin/sh', '/scripts/entrypoint-shard2.sh']
    healthcheck:
      test:
        [
          'CMD',
          'mongosh',
          '--quiet',
          '127.0.0.1/test',
          '--eval',
          "'quit(db.runCommand({ ping: 1 }).ok ? 0 : 2)'",
        ]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s
    logging:
      driver: 'json-file'
      options:
        max-size: '200k'
        max-file: '10'

  # Runs the Typesense
  typesense:
    image: typesense/typesense:27.1
    container_name: typesense
    env_file:
      - .env
    ports:
      - '${TYPESENSE_PORT}:8108'
    volumes:
      - typesense-data:/data
    environment:
      - TYPESENSE_API_KEY=${TYPESENSE_API_KEY}
      - TYPESENSE_DATA_DIR=/data
    command: /opt/typesense-server

  # Runs a Redis cache
  cache:
    image: redis:7
    container_name: redis
    ports:
      - '${REDIS_PORT}:6379'
    volumes:
      - rediscache:/data
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli ping | grep PONG']
      interval: 1s
      timeout: 3s
      retries: 5

  # Runs an Elasticsearch setup container to establish the certificates
  setup:
    container_name: elk-setup
    image: docker.elastic.co/elasticsearch/elasticsearch:${ELASTIC_STACK_VERSION}
    volumes:
      - certs:/usr/share/elasticsearch/config/certs
    user: '0'
    command: >
      bash -c '
        if [ x${ELASTICSEARCH_PASSWORD} == x ]; then
          echo "Set the ELASTICSEARCH_PASSWORD environment variable in the .env file";
          exit 1;
        elif [ x${KIBANA_PASSWORD} == x ]; then
          echo "Set the KIBANA_PASSWORD environment variable in the .env file";
          exit 1;
        fi;
        if [ ! -f config/certs/ca.zip ]; then
          echo "Creating CA";
          bin/elasticsearch-certutil ca --silent --pem -out config/certs/ca.zip;
          unzip config/certs/ca.zip -d config/certs;
        fi;
        if [ ! -f config/certs/certs.zip ]; then
          echo "Creating certs";
          echo -ne \
          "instances:\n"\
          "  - name: es01\n"\
          "    dns:\n"\
          "      - es01\n"\
          "      - localhost\n"\
          "    ip:\n"\
          "      - 127.0.0.1\n"\
          "  - name: kibana\n"\
          "    dns:\n"\
          "      - kibana\n"\
          "      - localhost\n"\
          "    ip:\n"\
          "      - 127.0.0.1\n"\
          > config/certs/instances.yml;
          bin/elasticsearch-certutil cert --silent --pem -out config/certs/certs.zip --in config/certs/instances.yml --ca-cert config/certs/ca/ca.crt --ca-key config/certs/ca/ca.key;
          unzip config/certs/certs.zip -d config/certs;
        fi;
        echo "Setting file permissions"
        chown -R root:root config/certs;
        find . -type d -exec chmod 750 \{\} \;;
        find . -type f -exec chmod 640 \{\} \;;
        echo "Waiting for Elasticsearch availability";
        until curl -s --cacert config/certs/ca/ca.crt https://es01:9200 | grep -q "missing authentication credentials"; do sleep 30; done;
        echo "Setting kibana_system password";
        until curl -s -X POST --cacert config/certs/ca/ca.crt -u "${ELASTICSEARCH_USERNAME}:${ELASTICSEARCH_PASSWORD}" -H "Content-Type: application/json" https://es01:9200/_security/user/kibana_system/_password -d "{\"password\":\"${KIBANA_PASSWORD}\"}" | grep -q "^{}"; do sleep 10; done;
        echo "All done!";
      '
    healthcheck:
      test: ['CMD-SHELL', '[ -f config/certs/es01/es01.crt ]']
      interval: 1s
      timeout: 5s
      retries: 120

  # Runs an Elasticsearch instance
  es01:
    container_name: elasticsearch
    depends_on:
      setup:
        condition: service_healthy
    image: docker.elastic.co/elasticsearch/elasticsearch:${ELASTIC_STACK_VERSION}
    labels:
      co.elastic.logs/module: elasticsearch
    volumes:
      - certs:/usr/share/elasticsearch/config/certs
      - esdata:/usr/share/elasticsearch/data
    ports:
      - ${ELASTICSEARCH_PORT}:9200
    environment:
      - node.name=elasticsearch
      - cluster.name=${ELASTICSEARCH_CLUSTER_NAME}
      - discovery.type=single-node
      - ELASTIC_PASSWORD=${ELASTICSEARCH_PASSWORD}
      - bootstrap.memory_lock=true
      - xpack.security.enabled=true
      - xpack.security.http.ssl.enabled=true
      - xpack.security.http.ssl.key=certs/es01/es01.key
      - xpack.security.http.ssl.certificate=certs/es01/es01.crt
      - xpack.security.http.ssl.certificate_authorities=certs/ca/ca.crt
      - xpack.security.transport.ssl.enabled=true
      - xpack.security.transport.ssl.key=certs/es01/es01.key
      - xpack.security.transport.ssl.certificate=certs/es01/es01.crt
      - xpack.security.transport.ssl.certificate_authorities=certs/ca/ca.crt
      - xpack.security.transport.ssl.verification_mode=certificate
      - xpack.license.self_generated.type=${ELASTIC_STACK_LICENSE}
    mem_limit: ${ELASTICSEARCH_MEM_LIMIT}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test:
        [
          'CMD-SHELL',
          "curl -s --cacert config/certs/ca/ca.crt https://localhost:9200 | grep -q 'missing authentication credentials'",
        ]
      interval: 10s
      timeout: 10s
      retries: 120

  # Runs a Kibana instance
  kibana:
    container_name: kibana
    depends_on:
      es01:
        condition: service_healthy
    image: docker.elastic.co/kibana/kibana:${ELASTIC_STACK_VERSION}
    labels:
      co.elastic.logs/module: kibana
    volumes:
      - certs:/usr/share/kibana/config/certs
      - kibanadata:/usr/share/kibana/data
    ports:
      - ${KIBANA_PORT}:5601
    environment:
      - SERVERNAME=kibana
      - ELASTICSEARCH_HOSTS=https://es01:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=${KIBANA_PASSWORD}
      - ELASTICSEARCH_SSL_CERTIFICATEAUTHORITIES=config/certs/ca/ca.crt
      - XPACK_SECURITY_ENCRYPTIONKEY=${ELASTIC_STACK_ENCRYPTION_KEY}
      - XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY=${ELASTIC_STACK_ENCRYPTION_KEY}
      - XPACK_REPORTING_ENCRYPTIONKEY=${ELASTIC_STACK_ENCRYPTION_KEY}
    mem_limit: ${KIBANA_MEM_LIMIT}
    healthcheck:
      test:
        [
          'CMD-SHELL',
          "curl -s -I http://localhost:5601 | grep -q 'HTTP/1.1 302 Found'",
        ]
      interval: 10s
      timeout: 10s
      retries: 120

  # Runs a Logstash instance
  logstash:
    container_name: logstash
    depends_on:
      es01:
        condition: service_healthy
      kibana:
        condition: service_healthy
    image: docker.elastic.co/logstash/logstash:${ELASTIC_STACK_VERSION}
    labels:
      co.elastic.logs/module: logstash
    user: root
    volumes:
      - certs:/usr/share/logstash/certs
      - logstashdata:/usr/share/logstash/data
      - './elastic/logstash/ingest_data/:/usr/share/logstash/ingest_data/'
      - './elastic/logstash/pipeline/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro'
      - './elastic/elasticsearch/templates/application-logs-template.json:/usr/share/logstash/config/elasticsearch/application-logs-template.json:ro'
    ports:
      - ${LOGSTASH_PORT}:5044
      - '9600:9600' # Expose the monitoring API port
    environment:
      - xpack.monitoring.enabled=false
      - ELASTIC_HOSTS=https://es01:9200
      - ELASTIC_USER=${ELASTICSEARCH_USERNAME}
      - ELASTIC_PASSWORD=${ELASTICSEARCH_PASSWORD}
      - LOGSTASH_PORT=${LOGSTASH_PORT}
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9600']
      interval: 30s
      timeout: 10s
      retries: 5

  kafka:
    container_name: kafka
    image: 'bitnami/kafka:4.0'
    user: root
    ports:
      - '9094:9094'
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093

      # Listeners: internal (9092), controller (9093), external (9094)
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://host.docker.internal:9094
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=PLAINTEXT

      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
      - KAFKA_KRAFT_CLUSTER_ID=abcdefghijklmnopqrstuv
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    networks:
      - kafka-network

  kafka-ui:
    image: provectuslabs/kafka-ui
    container_name: kafka-ui
    ports:
      - '8080:8080'
    environment:
      - DYNAMIC_CONFIG_ENABLED=true
      - KAFKA_CLUSTERS_0_NAME=qply
      - KAFKA_CLUSTERS_0_BOOTSTRAP_SERVERS=kafka:9092
    networks:
      - kafka-network

volumes:
  pgdata:
    driver: local
  mongodb_cluster_router_db:
    driver: local
  mongodb_cluster_router_config:
    driver: local
  mongodb_cluster_configsvr_db:
    driver: local
  mongodb_cluster_configsvr_config:
    driver: local
  mongodb_cluster_shard1_db:
    driver: local
  mongodb_cluster_shard1_config:
    driver: local
  mongodb_cluster_shard2_db:
    driver: local
  mongodb_cluster_shard2_config:
    driver: local
  rediscache:
    driver: local
  certs:
    driver: local
  esdata:
    driver: local
  kibanadata:
    driver: local
  logstashdata:
    driver: local
  typesense-data:
    driver: local

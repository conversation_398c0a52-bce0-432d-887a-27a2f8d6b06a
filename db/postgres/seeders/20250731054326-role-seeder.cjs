'use strict';

const rootEntityId = 'beb9aa2c-09fd-11f0-ba09-df67e743a9a1';
const rootDepartmentId = 'beb9aa2c-09fd-11f0-ba09-df67e743a9a2';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('Starting Admin Role Seeder...');

      const { default: RoleModel } = await import(
        '#src/modules/core/models/postgres/role.model.js'
      );
      const { default: RoleModuleModel } = await import(
        '#src/modules/core/models/postgres/role-module.model.js'
      );
      const { default: PolicySettingModel } = await import(
        '#src/modules/core/models/postgres/policy-setting.model.js'
      );
      const { default: ModuleModel } = await import(
        '#src/modules/core/models/postgres/module.model.js'
      );
      const { RoleConstant } = await import('#src/modules/user/constants/index.js');
      const { CoreConstant } = await import('#src/modules/core/constants/index.js');

      const { Op } = await import('sequelize');

      const roleModel = RoleModel({ psql: { connection: queryInterface.sequelize } });
      const roleModuleModel = RoleModuleModel({ psql: { connection: queryInterface.sequelize } });
      const policySettingModel = PolicySettingModel({
        psql: { connection: queryInterface.sequelize },
      });
      const moduleModel = ModuleModel({ psql: { connection: queryInterface.sequelize } });

      const POLICIES = RoleConstant.POLICIES;

      const now = new Date();
      const [adminRole] = await roleModel.findOrCreate({
        where: { name: 'Super Admin', entityId: rootEntityId },
        defaults: {
          name: 'Super Admin',
          entityId: rootEntityId,
          departmentId: rootDepartmentId,
          description: 'Role with all system policies',
          status: CoreConstant.COMMON_STATUSES.ACTIVE,
          path: 'super_admin',
          createdAt: now,
          updatedAt: now,
          createdBy: null,
          updatedBy: null,
        },
        transaction,
      });

      const modules = await moduleModel.findAll({
        where: {
          navigationUrl: {
            [Op.ne]: null,
          },
        },
        transaction,
      });

      for (const module of modules) {
        const [roleModule] = await roleModuleModel.findOrCreate({
          where: { roleId: adminRole.id, moduleId: module.id },
          defaults: {
            roleId: adminRole.id,
            moduleId: module.id,
            createdAt: now,
            updatedAt: now,
            createdBy: null,
            updatedBy: null,
          },
          transaction,
        });

        const policyDefaults = {};
        POLICIES.forEach((policy) => {
          policyDefaults[policy] = true;
        });

        await policySettingModel.findOrCreate({
          where: { parentId: roleModule.id },
          defaults: {
            parentId: roleModule.id,
            ...policyDefaults,
            createdAt: now,
            updatedAt: now,
            createdBy: null,
            updatedBy: null,
          },
          transaction,
        });
      }

      await transaction.commit();
      console.log('Admin Role Seeder completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error in Admin Role Seeder:', error);
      throw error;
    }
  },

  async down(queryInterface) {},
};

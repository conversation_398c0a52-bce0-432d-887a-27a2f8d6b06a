import i18n from '@/providers/i18n/i18n';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, within } from 'storybook/test';
import Page from './page';

const meta = {
  title: 'Pages/Auth/PendingApprovalPage',
  component: Page,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      return <Story />;
    },
  ],
} satisfies Meta<typeof Page>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Story for the default PendingApprovalPage component.
 */
export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Should render refresh button', async () => {
      expect(canvas.getByTestId('refresh')).toBeInTheDocument();
    });
  },
};

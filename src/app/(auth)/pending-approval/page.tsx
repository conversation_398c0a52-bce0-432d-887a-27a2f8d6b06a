'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { PendingApprovalForm } from '@/components/features/auth/forms';
import { GuestGuard as Layout } from '@/components/features/auth/guards/guest-guard';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const Page = (): JSX.Element => {
  usePageTitle('common.label.accountPendingApproval');
  return (
    <Layout>
      <AuthLayout>
        <PendingApprovalForm />
      </AuthLayout>
    </Layout>
  );
};

export default Page;

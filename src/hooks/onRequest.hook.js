import fp from 'fastify-plugin';
import i18next from 'i18next';

import { CoreError } from '#src/modules/core/errors/index.js';
import { bypassAccessCheck } from '#src/utils/access.util.js';
import { decodeJWT } from '#src/utils/jwt.util.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Extracts the language code from the 'accept-language' header.
 */
const extractLanguage = (acceptLanguage) => {
  if (!acceptLanguage) {
    return i18next.options.fallbackLng;
  }
  const [lang] = acceptLanguage.split(',').map((l) => l.split(';')[0]);
  return lang?.split('-')[0] || i18next.options.fallbackLng;
};

/**
 * Extracts a safe entity access ID from the 'origin' header.
 * (Customize as needed for your application's URL structure.)
 */
const extractEntityAccessId = (originHeader) => {
  try {
    if (!originHeader) {
      return '193845234911';
    }
    const url = new URL(originHeader);
    // Example: get the first path segment after '/' (change if your structure differs)
    return url.pathname.split('/')[1] || '193845234911';
  } catch {
    return '193845234911';
  }
};

/**
 * Extracts the Bearer token from an Authorization header.
 */
const extractToken = (authorization) => (authorization || '').replace(/^Bearer\s+/i, '') || null;

/**
 * Maps entity data from decoded JWT info.
 */
const mapEntity = (entityInfo) =>
  entityInfo
    ? {
        id: entityInfo.id,
        hierarchy: entityInfo.hierarchy,
        code: entityInfo.code,
        prefix: entityInfo.prefix,
        name: entityInfo.name,
      }
    : null;

const mapAuthInfo = (authInfo) =>
  authInfo
    ? {
        id: authInfo.id,
        authAccess: authInfo.authAccess,
        username: authInfo.username,
        role: authInfo.role,
        roleId: authInfo.roleId,
        department: authInfo.department,
        fingerprintId: authInfo.fingerprintId,
      }
    : null;

/**
 * Fastify onRequest hook plugin.
 */
const onRequestHook = (fastify) => {
  fastify.addHook('onRequest', async (request) => {
    fastify.log.debug('Executing onRequest hook');
    request.startTime = Date.now();
    request.id = uuidv4();

    // Language and locale
    const acceptLanguage = request.headers['accept-language'];
    request.language = extractLanguage(acceptLanguage);
    request.locale = acceptLanguage;

    await i18next.changeLanguage(request.language);

    // Entity context
    request.entityAccessId = extractEntityAccessId(request.headers.origin);
    request.hierarchyLevel = 'merchant';

    // JWT & user info
    if (!bypassAccessCheck(request)) {
      const token = extractToken(request.headers.authorization);
      // No JWT token provided, reject request
      if (!token) {
        throw CoreError.unauthorised();
      }

      try {
        const tokenInfo = await decodeJWT(token, fastify);
        const basic = tokenInfo?.basicInformation || {};
        request.userEntity = mapEntity(basic.userEntity);
        request.parentEntity = mapEntity(basic.parentEntity);
        request.entity = mapEntity(basic.entity);
        request.authInfo = mapAuthInfo(basic.authInfo);
      } catch (error) {
        fastify.log.debug(error, 'Failed to decode JWT token');
        request.authInfo = null;
        request.userEntity = null;
        request.parentEntity = null;
        request.entity = null;
      }
    }
  });
};

export { extractLanguage, extractEntityAccessId, extractToken, mapEntity, mapAuthInfo };

export default fp(onRequestHook, { name: 'onRequestHook' });

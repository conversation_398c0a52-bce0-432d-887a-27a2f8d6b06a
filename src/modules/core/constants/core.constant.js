/* eslint-disable sonarjs/no-hardcoded-passwords */
export const ACCESS_LEVELS = {
  MERCHANT: 'merchant',
  ORGANIZATION: 'organization',
  ROOT: 'root',
};

export const ACCESS_LEVEL_KEYS = {
  user: 'user',
  member: 'member',
  webhook: 'webhook',
};

export const PERMISSION_FIELDS = [
  'canRead',
  'canCreate',
  'canEdit',
  'canManage',
  'canImport',
  'canExport',
];

export const CACHE_SECOND = {
  SHORT: 10, // For rapidly changing data or debounce-type caching
  MEDIUM: 30, // 30 second – suitable for moderately volatile data
  STANDARD: 60, // 1 minute – good default for most general cache
  LONG: 3600, // 1 hour – stable data that changes infrequently
  DAILY: 86400, // 24 hours – rarely changing reference data
  WEEKLY: 604800, // 7 days – archive-type or external lookup cache
  NEVER: 0, // Used when caching is disabled
};

export const COMMON_STATUSES = {
  ACTIVE: 'active',
  DELETED: 'deleted',
  INACTIVE: 'inactive',
};

export const EVENTS = {
  APP_CENTER: 'appCenter',
  AUDIT_TRAIL: 'auditTrail',
  AUTOMATION: 'automation',
  CREDIT_LIMIT_SETTING: 'creditLimitSetting',
  CURRENCY_SETTING: 'currencySetting',
  DATA_MASKING_SETTING: 'dataMaskingSetting',
  DEVELOPER_HUB: 'developerHub',
  GAME_PROVIDER: 'gameProvider',
  LANGUAGE_SETTING: 'languageSetting',
  LOCALISATION_SETTING: 'localisationSetting',
  LOGIN: 'login',
  LOGOUT: 'logout',
  MAINTENANCE: 'maintenance',
  MANAGE_MONITOR: 'manageMonitor',
  MEDIA: 'media',
  MEMBER_POINT: 'memberPoint',
  MEMBER_VIP: 'memberVIP',
  MEMBER: 'member',
  MERCHANT_CREDIT: 'merchantCredit',
  MERCHANT: 'merchant',
  ORGANIZATION: 'organization',
  OTP: 'OTP',
  PERSONAL_SETTING: 'personalSetting',
  REGION_SETTING: 'regionSetting',
  REGISTRATION_FORM: 'registrationForm',
  RESET_PASSWORD: 'resetPassword',
  RISK_GROUP: 'riskGroup',
  ROLE: 'role',
  SECURITY_ACCESS_CONTROL: 'securityAccessControl',
  SETTING: {
    PERSONAL: 'personalSetting',
    SAFETY: 'safety',
    THEME: 'themesSetting',
  },
  SETTING_OPTIONS: {
    PERSONAL: 'personalSettingsOptions',
    SAFETY: 'safetySettingsOptions',
    THEME: 'themeSettingsOptions',
  },
  TAG: 'tag',
  TRANSACTION: 'transaction',
  TRIGGERED_EVENT_LOG: 'triggeredEventLog',
  TWO_FACTOR_AUTHENTICATION: 'twoFactorAuthentication',
  USER_AUDIT_TRAIL: 'userAuditTrail',
  USER_EXTERNAL_INVITATION: 'userExternalInvitation',
  USER_HIERARCHY: 'userHierarchy',
  USER_LOGIN_LOG: 'userLoginLog',
  USER_SSO: 'userSSO',
  USER_SUB_ACCOUNT: 'userSubAccount',
  USER: 'user',
};

export const EVENT_ACTIONS = {
  API_KEY_GENERATED: 'apiKeyGenerated',
  APP_INSTALLED: 'appInstalled',
  APP_UNINSTALLED: 'appUninstalled',
  ARCHIVED: 'archived',
  ASSIGNED: 'assigned',
  CREATED: 'created',
  DELETED: 'deleted',
  DETAILS_VIEWED: 'detailsViewed',
  DUPLICATED: 'duplicated',
  EDITED: 'edited',
  EXPORTED: 'exported',
  IMPORTED: 'imported',
  IP_BLACKLISTED: 'ipBlacklisted',
  KILL_SWITCH_ACTIVATED: 'killSwitchActivated',
  KILL_SWITCH_DEACTIVATED: 'killSwitchDeactivated',
  LOGIN: 'login',
  LOGOUT: 'logout',
  SEARCHED: 'searched',
  SETUP: 'setup',
  STATUS_UPDATED: 'statusUpdated',
  TEST_RAN: 'testRan',
  UNKNOWN_ACTION: 'unknownAction',
  UPDATED: 'updated',
  VIEWED: 'viewed',
};

export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const MODULE_METHODS = {
  CREATE: 'create',
  DELETE: 'delete',
  EXPORT: 'export',
  INDEX: 'index',
  NAVIGATION: 'navigation',
  OPTION: 'option',
  UPDATE_ACCESS_CONTROL: 'updateAccessControls',
  UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
  UPDATE_MODULE_POLICY: 'updateModulePolicy',
  UPDATE_PERMISSION: 'updatePermissions',
  UPDATE_PERSONAL: 'updatePersonal',
  UPDATE_SAFETY: 'updateSafety',
  UPDATE_STATUS: 'updateStatus',
  UPDATE_THEMES: 'updateThemes',
  UPDATE: 'update',
  VIEW: 'view',
};

export const MODULE_NAMES = {
  ACCESS_CONTROL: 'accessControls',
  AUDIT_TRAIL: 'auditTrails',
  BULK_JOB: 'bulkJobs',
  CORE: 'core',
  DEPARTMENT_TEMPLATE: 'departmentTemplates',
  DEPARTMENT: 'departments',
  DEVELOPER_HUB: 'developerHubs',
  LOCALISATION: 'localisations',
  ROLE: 'roles',
  SETTING: 'settings',
};

export const REDACT_FIELDS = {
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirmPassword',
};

export const REMARK_STATUSES = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
};

export const REMARK_TYPE = {
  AUDIT: 'audit',
  NOTE: 'note',
  SECURITY: 'security',
  SYSTEM: 'system',
  WARNING: 'warning',
};

export const REMARKABLE_TYPE = {
  IP_ACCESS_CONTROL: 'ip_access_control',
};

export const VIEW_ACTION = {
  SEARCHED: 'searched',
  VIEWED: 'viewed',
  DETAILS_VIEWED: 'detailsViewed',
};

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const CORE_ERROR_DEF = {
  unknownError: ['10000', 'error.sentence.internal', 500],
  notFound: ['10001', 'error.sentence.notFound', 404],
  dataNotFound: ['10002', 'error.sentence.dataNotFound', 404],
  unauthorised: ['10003', 'error.sentence.unauthorised', 401],
  forbidden: ['10004', 'error.sentence.forbidden', 403],
  tooManyRequests: ['10005', 'error.sentence.tooManyRequests', 429],
  versionConflict: ['10013', 'error.sentence.versionConflict', 409],
  unprocessable: ['10015', 'error.sentence.unprocessable', 422],
  serviceUnavailable: ['10016', 'error.sentence.serviceUnavailable', 503],
};

export const coreError = createModuleErrors(MODULE_NAMES.CORE, CORE_ERROR_DEF);

import * as ApiRightModel from '#src/modules/core/models/postgres/api-right.model.js';
import * as AuditTrailModel from '#src/modules/core/models/mongo/audit-trail.model.js';
import * as BulkJobModel from '#src/modules/core/models/postgres/bulk-job.model.js';
import * as CustomLocalisationModel from '#src/modules/core/models/postgres/custom-localisation.model.js';
import * as CustomSettingModel from '#src/modules/core/models/postgres/custom-setting.model.js';
import * as DeveloperHubModel from '#src/modules/core/models/postgres/developer-hub.model.js';
import * as IpAccessControlModel from '#src/modules/core/models/postgres/ip-access-control.model.js';
import * as LocalisationModel from '#src/modules/core/models/postgres/localisation.model.js';
import * as RemarkModel from '#src/modules/core/models/postgres/remark.model.js';
import * as RoleModel from '#src/modules/core/models/postgres/role.model.js';
import * as RoleModuleModel from '#src/modules/core/models/postgres/role-module.model.js';
import * as SettingModel from '#src/modules/core/models/postgres/setting.model.js';

export {
  ApiRightModel,
  AuditTrailModel,
  BulkJobModel,
  CustomLocalisationModel,
  CustomSettingModel,
  DeveloperHubModel,
  IpAccessControlModel,
  LocalisationModel,
  RemarkModel,
  RoleModel,
  RoleModuleModel,
  SettingModel,
};

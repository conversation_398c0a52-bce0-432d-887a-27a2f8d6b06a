import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class RoleModule extends Model {
    static associate(models) {
      RoleModule.hasOne(models.PolicySetting, {
        foreignKey: 'parentId',
        as: 'policySetting',
      });
      RoleModule.belongsTo(models.Module, {
        foreignKey: 'moduleId',
        as: 'module',
      });
      RoleModule.belongsTo(models.Role, {
        foreignKey: 'roleId',
        as: 'role',
      });
    }
  }

  RoleModule.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      roleId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
      },
      moduleId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'modules',
          key: 'id',
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'RoleModule',
      tableName: 'role_modules',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(RoleModule);

  return RoleModule;
}

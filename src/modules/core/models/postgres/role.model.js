import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { RoleConstant } from '#src/modules/user/constants/index.js';

export default function (fastify, instance) {
  class Role extends Model {
    static associate(models) {
      this.hasMany(models.RoleModule, { foreignKey: 'roleId', as: 'modulePolicies' });

      this.belongsTo(models.Role, {
        as: 'parentRole',
        foreignKey: 'parentId',
      });

      this.hasMany(models.Role, {
        as: 'childRoles',
        foreignKey: 'parentId',
      });
    }
  }

  Role.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'roles',
          key: 'id',
        },
      },
      parentName: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.parentRole?.name;
        },
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'entities',
          key: 'id',
        },
      },
      departmentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'departments',
          key: 'id',
        },
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(RoleConstant.STATUSES)),
        allowNull: false,
        defaultValue: RoleConstant.STATUSES.ACTIVE,
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      version: {
        type: DataTypes.BIGINT,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1,
          notNull: { msg: 'version is required' },
        },
      },
    },
    {
      modelName: 'Role',
      tableName: 'roles',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(Role);
  versionedMixin.applyVersioning(Role);

  return Role;
}

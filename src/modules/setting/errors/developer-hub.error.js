import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const DEVELOPER_HUB_ERROR_DEF = {
  apiKeyExists: ['54409', 'error.developerHub.sentence.apiKeyExists', 409],
  invalidData: ['54422', 'error.developerHub.sentence.invalidData', 422],
};

export const developerHubError = createModuleErrors(
  MODULE_NAMES.DEVELOPER_HUB,
  DEVELOPER_HUB_ERROR_DEF,
);

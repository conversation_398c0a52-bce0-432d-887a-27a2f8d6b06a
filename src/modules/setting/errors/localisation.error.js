// errors/localisationErrors.js
import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const LOCALISATION_ERROR_DEF = {
  inUse: ['51409', 'error.localisations.sentence.inUse', 409],
  invalidData: ['51400', 'error.localisations.sentence.invalidData', 400],
  unsupportedUpdate: ['51422', 'error.localisations.sentence.unsupportedUpdate', 422],
};

export const localisationError = createModuleErrors(
  MODULE_NAMES.LOCALISATION,
  LOCALISATION_ERROR_DEF,
);

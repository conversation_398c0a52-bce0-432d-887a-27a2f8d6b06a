import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { LocalisationConstant } from '#src/modules/setting/constants/index.js';
import { LocalisationError } from '#src/modules/setting/errors/index.js';
import { LocalisationRepository } from '#src/modules/setting/repository/index.js';
import { computeUpdateDiff } from '#src/utils/audit-trail.util.js';
import { fetchFromCache } from '#src/utils/cache.util.js';

const { LOCALISATION_CATEGORIES } = LocalisationConstant;
const { CURRENCY, LANGUAGE, REGION } = LOCALISATION_CATEGORIES;

const { ROOT, ORGANIZATION, MERCHANT } = CoreConstant.ACCESS_LEVELS;
const { INACTIVE } = CoreConstant.COMMON_STATUSES;

const CHILD_ENTITY_MAP = {
  [ROOT]: ORGANIZATION,
  [ORGANIZATION]: MERCHANT,
};

/**
 * Retrieves all localisation entries for a given entity.
 * @param {Object} request - The request object containing entity information.
 * @returns {Promise<Object>} A promise that resolves to the list of localisation entries.
 */
export const index = async (request) => {
  const { entity, server } = request;
  const { code } = server.systemSetting.baseCurrency;

  const query = {
    ...request.query,
    filter_entityId_eq: entity.id,
  };

  const res = await LocalisationRepository.findAll(request.server, query);

  const mapBaseCurrency = res.rows.map((row) => ({
    ...row.toJSON(),
    baseCurrency: code,
  }));

  return {
    ...res,
    rows: mapBaseCurrency,
  };
};

/**
 * Generates a dropdown of localisation entries for a given category and entity.
 * @param {Object} request - The request object containing query parameters and entity information.
 * @returns {Promise<Array>} A promise that resolves to an array of localisation entries with id and name.
 */
export const generateDropdown = async (request) => {
  const { filter_category } = request.query;
  const { entity, server } = request;

  const cacheKey = `${filter_category}_${entity.id}`;

  return fetchFromCache(server.redis, cacheKey, async () => {
    const allLocalisations = await index(request);
    return allLocalisations.rows.map(({ id, name }) => ({ id, name }));
  });
};

/**
 * Retrieves a specific localisation entry by ID.
 * @param {Object} request - The request object containing params, body, and entity information.
 * @returns {Promise<Object>} A promise that resolves to the localisation entry.
 * @throws {LocalisationError} If the localisation is not found, custom localisation is not found, or there's a version conflict.
 */
export const view = async (request, id) => {
  const { entity, server } = request;
  const { code } = server.systemSetting.baseCurrency;

  const query = {
    filter_id_eq: id,
    filter_entityId_eq: entity.id,
  };

  const localisation = await LocalisationRepository.findById(server, query);
  if (!localisation) {
    throw CoreError.dataNotFound({ data: 'common.label.localisation', id });
  }

  const localisationJson = localisation.toJSON();

  return {
    ...localisationJson,
    baseCurrency: code,
  };
};

/**
 * Updates a localisation entry.
 * @param {Object} request - The request object containing params and body.
 * @returns {Promise<Object>} A promise that resolves to the updated localisation entry.
 */
export const update = async (request, id) => {
  const { entity, server, body, authInfo } = request;
  const { exchangeRate } = body;

  const query = {
    filter_id_eq: id,
    filter_entityId_eq: entity.id,
  };

  const localisation = await LocalisationRepository.findById(server, query);
  if (!localisation) {
    throw CoreError.dataNotFound({ data: 'common.label.localisation', id });
  }

  switch (localisation.category) {
    case CURRENCY:
      if (!exchangeRate) {
        throw LocalisationError.invalidData('error.localisations.sentence.exchangeRateRequired');
      }
      break;
    case LANGUAGE:
    case REGION:
    default:
      throw LocalisationError.unsupportedUpdate(localisation.category);
  }

  // Compute before state and fields changed
  const { beforeState, fieldsChanged } = computeUpdateDiff(localisation, body);

  const updated = await LocalisationRepository.update(localisation, body, authInfo);

  const referenceDetails = {
    category: localisation.category,
    code: localisation.code,
  };

  return {
    result: updated.toJSON(),
    audit: {
      beforeState,
      afterState: updated.toJSON(),
      fieldsChanged,
      referenceDetails,
    },
  };
};

/**
 * Updates the status of a localisation entry.
 * @param {Object} request - The request object containing params, body, and entity information.
 * @returns {Promise<Object>} A promise that resolves to the updated localisation entry.
 * @throws {LocalisationError} If the localisation is in use by child entities when trying to deactivate.
 */
export const updateStatus = async (request, id) => {
  const { body, entity, server, authInfo } = request;
  const { status } = body;

  const query = {
    filter_id_eq: id,
    filter_entityId_eq: entity.id,
  };

  const localisation = await LocalisationRepository.findById(server, query);
  if (!localisation) {
    throw CoreError.dataNotFound({ data: 'common.label.localisation', id });
  }

  if (status === INACTIVE && [ROOT, ORGANIZATION].includes(entity.hierarchy)) {
    const childLevel = CHILD_ENTITY_MAP[entity.hierarchy];

    if (childLevel) {
      // SC-01: Replace with actual entity repo query
      const childEntityIds = ['beb9aa2c-09fd-11f0-ba09-df67e743a999'];

      const query = {
        filter_entityId_in: childEntityIds.join(','),
        filter_parentId_eq: localisation.parentId,
      };

      if (childEntityIds.length) {
        const inUse = await LocalisationRepository.findActiveByParentId(server, query);
        if (inUse) {
          throw LocalisationError.inUse(localisation.id);
        }
      }
    }
  }

  // Compute before state and fields changed
  const { beforeState, fieldsChanged } = computeUpdateDiff(localisation, body);

  const updated = await LocalisationRepository.update(localisation, body, authInfo);

  return {
    result: updated.toJSON(),
    audit: {
      beforeState,
      afterState: updated.toJSON(),
      fieldsChanged,
    },
  };
};

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ROLE_ERROR_DEF = {
  exceedingParentPolicies: ['80003', 'error.role.sentence.exceedingParentPolicies', 400],
  invalidData: ['80002', 'error.role.sentence.invalidData', 400],
  invalidHierarchy: ['80006', 'error.role.sentence.invalidHierarchy', 400],
  moduleNotFound: ['80010', 'error.role.sentence.moduleNotFound', 404],
  nameNotUnique: ['80007', 'error.role.sentence.nameNotUnique', 409],
  notAuthorizedToUpdate: ['80005', 'error.role.sentence.notAuthorizedToUpdate', 403],
  notFound: ['80001', 'error.role.sentence.notFound', 404],
  parentRoleNotFound: ['80009', 'error.role.sentence.parentRoleNotFound', 404],
  unauthorizedAccess: ['80008', 'error.role.sentence.unauthorizedAccess', 403],
  unsupportedPolicies: ['80004', 'error.role.sentence.unsupportedPolicies', 400],
};

export const roleError = createModuleErrors(MODULE_NAMES.ROLE, ROLE_ERROR_DEF);

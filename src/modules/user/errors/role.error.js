import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ROLE_ERROR_DEF = {
  exceedingParentPolicies: [
    '80003',
    'Cannot assign policies that exceed parent role permissions',
    400,
  ],
  invalidData: ['80002', 'Invalid role data: %s', 400],
  invalidHierarchy: ['80006', 'Invalid hierarchy for module: %s', 400],
  moduleNotFound: ['80010', 'Module not found with ID: %s', 404],
  nameNotUnique: ['80007', 'Role name already exists for this entity', 409],
  notAuthorizedToUpdate: ['80005', 'Not authorized to update this role', 403],
  notFound: ['80001', 'Role not found with ID: %s', 404],
  parentRoleNotFound: ['80009', 'Parent role not found with ID: %s', 404],
  unauthorizedAccess: ['80008', 'Unauthorized.', 403],
  unsupportedPolicies: ['80004', 'Requested policies are not supported: %s', 400],
};

export const roleError = createModuleErrors(MODULE_NAMES.ROLE, ROLE_ERROR_DEF);

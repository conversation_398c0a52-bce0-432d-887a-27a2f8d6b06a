import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { RoleService } from '#src/modules/user/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { ROLE },
  MODULE_METHODS: { INDEX, VIEW, CREATE, UPDATE, UPDATE_STATUS, OPTION, NAVIGATION },
} = CoreConstant;

const MODULE = ROLE;

/**
 * Handles the retrieval of role data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the role data and pagination details.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => RoleService.index(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Retrieves a specific role entry by ID.
 *
 * @param {Object} request - The request object containing the ID parameter.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the role entry.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => RoleService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the creation of a new role.
 *
 * @param {Object} request - The request object containing the new role data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the newly created role.
 */
export const create = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: RoleService.create,
    module: MODULE,
    method: CREATE,
  });
};

/**
 * Updates a role entry.
 *
 * @param {Object} request - The request object containing the ID parameter and the updated data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated role entry.
 */
export const update = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: RoleService.update,
    module: MODULE,
    method: UPDATE,
  });

/**
 * Updates the status of a role entry.
 *
 * @param {Object} request - The request object containing the ID parameter and the updated status.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated role entry.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: RoleService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Handles the options request for roles.
 *
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The HTTP response object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing department options data.
 */
export const options = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => RoleService.options(request),
    module: MODULE,
    method: OPTION,
  });

/**
 * Retrieves navigation data associated with roles.
 *
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The HTTP response object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            containing navigation data for roles.
 */
export const navigations = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => RoleService.navigations(request),
    module: MODULE,
    method: NAVIGATION,
  });

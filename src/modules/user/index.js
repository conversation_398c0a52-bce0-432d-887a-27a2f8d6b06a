import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '#src/modules/user/handlers/index.js';
import { DepartmentRepository, RoleRepository } from '#src/modules/user/repository/index.js';
import {
  DepartmentRoute,
  DepartmentTemplateRoute,
  RoleRoute,
} from '#src/modules/user/routes/index.js';
import { DepartmentSchema, RoleSchema } from '#src/modules/user/schemas/index.js';
import { DepartmentService, RoleService } from '#src/modules/user/services/index.js';

export {
  DepartmentHandler,
  DepartmentRepository,
  DepartmentRoute,
  DepartmentSchema,
  DepartmentService,
  DepartmentTemplateRoute,
  RoleHandler,
  RoleRepository,
  RoleRoute,
  RoleSchema,
  RoleService,
};

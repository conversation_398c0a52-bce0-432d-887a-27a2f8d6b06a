/**
 * Find a module by primary key
 * @param {Object} fastify - The fastify instance
 * @param {string} id - The module ID
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The module
 */
export const findById = async (fastify, id, options = {}) => {
  const { Module } = fastify.psql;
  return await Module.findByPk(id, options);
};

/**
 * Find all module primary key
 * @param {Object} fastify - The fastify instance
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The module
 */
export const findAll = async (fastify, options = {}) => {
  const { Module } = fastify.psql;
  return await Module.findAll(options);
};

/**
 * Find a policy setting by parent ID
 * @param {Object} fastify - The fastify instance
 * @param {string} parentId - The parent ID
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The policy setting
 */
export const findByParentId = async (fastify, parentId, options = {}) => {
  const { PolicySetting } = fastify.psql;
  return await PolicySetting.findOne({
    where: { parentId },
    ...options,
  });
};

/**
 * Find or create a policy setting
 * @param {Object} fastify - The fastify instance
 * @param {Object} where - The where clause
 * @param {Object} defaults - The default values
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - [policySetting, created]
 */
export const findOrCreate = async (fastify, where, defaults, options = {}) => {
  const { PolicySetting } = fastify.psql;
  return await PolicySetting.findOrCreate({
    where,
    defaults,
    ...options,
  });
};

/**
 * Upsert a policy setting
 * @param {Object} fastify - The fastify instance
 * @param {Object} values - The values to upsert
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The upserted policy setting
 */
export const upsert = async (fastify, values, options = {}) => {
  const { PolicySetting } = fastify.psql;
  return await PolicySetting.upsert(values, options);
};

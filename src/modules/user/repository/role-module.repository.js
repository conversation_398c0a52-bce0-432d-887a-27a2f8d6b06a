/**
 * Find all role module policies with their associated policy settings and modules for a specific role
 * @param {Object} server - The server instance
 * @param {string} roleId - The ID of the role
 * @returns {Promise<Array>} - Array of role module policies with their policy settings and modules
 */
export const findAllByRoleIdWithPolicySettingsAndModules = async (fastify, roleId) => {
  const { Module, PolicySetting, RoleModule } = fastify.psql;
  return await RoleModule.findAll({
    where: { roleId },
    include: [
      {
        model: PolicySetting,
        as: 'policySetting',
      },
      {
        model: Module,
        as: 'module',
      },
    ],
  });
};

/**
 * Find or create a role module policy
 * @param {Object} fastify - The fastify instance
 * @param {Object} where - The where clause
 * @param {Object} defaults - The default values
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - [RoleModule, created]
 */
export const findOrCreate = async (fastify, where, defaults, options = {}) => {
  const { RoleModule } = fastify.psql;
  return await RoleModule.findOrCreate({
    where,
    defaults,
    ...options,
  });
};

/**
 * Find all role module policies by role ID
 * @param {Object} fastify - The fastify instance
 * @param {string} roleId - The role ID
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - The role module policies
 */
export const findAllByRoleId = async (fastify, roleId, options = {}) => {
  const { RoleModule } = fastify.psql;
  return await RoleModule.findAll({
    where: { roleId },
    ...options,
  });
};

/**
 * Update a role module policy
 * @param {Object} RoleModule - The role module policy to update
 * @param {Object} data - The data to update
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The updated role module policy
 */
export const update = async (roleModule, data, options = {}) => {
  return await roleModule.update(data, options);
};

/**
 * Find all role module policies by role ID with policy settings
 * @param {Object} server - The server instance
 * @param {string} roleId - The role ID
 * @returns {Promise<Array>} - The role module policies with policy settings
 */
export const findAllByRoleIdWithPolicySettings = async (server, roleId) => {
  return await server.psql.connection.models.RoleModule.findAll({
    where: { roleId },
    include: [
      {
        model: server.psql.connection.models.PolicySetting,
        as: 'policySetting',
      },
    ],
  });
};

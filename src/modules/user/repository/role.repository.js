import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

import { Sequelize } from 'sequelize';

/**
 * Retrieves all role entries with pagination based on the given request and entity ID.
 * Includes the parent role name for each role.
 *
 * @param {Object} request - The request object containing query parameters and server instance.
 * @param {string|number} entityId - The ID of the entity to filter the role entries.
 * @returns {Promise<Object>} A promise that resolves to the paginated result of role entries with parent names.
 */
export const findAll = async (fastify, query) => {
  const { Role } = fastify.psql;

  const includes = [
    {
      model: Role,
      as: 'parentRole',
      attributes: ['name'],
      required: false,
    },
  ];

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    Role,
    includes,
  );

  const result = await applyOffsetPagination(fastify, Role, query, whereFilter, includeFilter, [
    'id',
    'name',
    'status',
    'createdAt',
    'updatedAt',
  ]);

  return result;
};

/**
 * Retrieves a role entry by its ID, scoped to specific role IDs.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Array<string|number>} roleIds - An array of role IDs to scope the search.
 * @param {string|number} id - The unique identifier of the role entry to retrieve.
 * @returns {Promise<Object|null>} A promise that resolves to the found role entry or null if not found.
 */
export const findById = async (fastify, id) => {
  const { Role } = fastify.psql;

  const includes = [
    {
      model: Role,
      as: 'parentRole',
      attributes: ['name'],
      required: false,
    },
  ];

  const record = await Role.findByPk(id, {
    include: includes,
  });

  return record;
};

/**
 * Retrieves all roles with a path that starts with the given parent path.
 *
 * @param {Object} server - The server instance providing access to the database.
 * @param {string|number} entityId - The ID of the entity to filter the role entries.
 * @param {string} parentPath - The parent path to match against role paths.
 * @param {Object} options - Additional options for the query (e.g., transaction).
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of role entries.
 */
export const findAllByParentPath = async (server, entityId, parentPath, options = {}) => {
  const { Role } = server.psql;

  const record = await Role.findAll({
    where: {
      entityId: entityId,
      [Sequelize.Op.or]: [
        Sequelize.literal(`"path" = '${parentPath}'::ltree`),
        Sequelize.literal(`"path" <@ '${parentPath}'::ltree`),
      ],
    },
    ...options,
  });

  return record;
};

/**
 * Creates a new role entry in the database.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {string|number} roleId - The ID of the role creating the role.
 * @param {Object} roleData - An object containing the data for the new role.
 * @returns {Promise<Object>} A promise that resolves to the newly created role entry.
 */
export const create = async (fastify, roleData, options = {}) => {
  const newRole = await fastify.psql.Role.create(roleData, options);

  return newRole.toJSON();
};

/**
 * Updates a role entry with new data.
 *
 * @param {Object} modelData - The current role model instance to be updated.
 * @param {Object} updateData - An object containing the fields and values to update in the role entry.
 * @returns {Promise<Object>} A promise that resolves to the updated role entry.
 */
export const update = async (modelData, updateData, options = {}) => {
  return await modelData.update(updateData, options);
};

/**
 * Updates the status of a role entry.
 *
 * @param {Object} modelData - The current role model instance to be updated.
 * @param {string|number} status - The new status to be set for the role entry.
 * @returns {Promise<Object>} A promise that resolves to the updated role entry with the new status.
 */
export const updateStatus = async (modelData, status, options = {}) => {
  return await modelData.update({ status }, options);
};

/**
 * Retrieves a role by ID along with its associated module policies.
 *
 * @param {Object} server - The server instance providing access to the database.
 * @param {string|number} entityId - The ID of the entity to filter the role entries.
 * @param {string|number} roleId - The ID of the role to retrieve.
 * @returns {Promise<Object|null>} A promise that resolves to the role with its module policies, or null if not found.
 */
export const findByIdWithModulePolicies = async (server, entityId, roleId) => {
  const { Role, PolicySetting, Module } = server.psql.connection.models;

  const role = await Role.findOne({
    where: { id: roleId, entityId },
    include: [
      {
        association: 'parentRole',
        attributes: ['name'],
        required: false,
      },
    ],
  });

  if (!role) {
    return null;
  }

  const roleModulePolicies = await role.getModulePolicies({
    include: [
      {
        model: PolicySetting,
        as: 'policySetting',
      },
      {
        model: Module,
        as: 'module',
      },
    ],
  });

  const modulePoliciesByHierarchy = roleModulePolicies.reduce((acc, modulePolicy) => {
    const { moduleId, module, policySetting } = modulePolicy;
    const hierarchy = module.hierarchy;

    if (!acc[hierarchy]) {
      acc[hierarchy] = [];
    }

    const policySettings = policySetting ? policySetting.toJSON() : {};

    delete policySettings.id;
    delete policySettings.parentId;
    delete policySettings.createdAt;
    delete policySettings.updatedAt;
    delete policySettings.createdBy;
    delete policySettings.updatedBy;

    acc[hierarchy].push({
      id: moduleId,
      name: module.name,
      policySettings: policySettings,
    });

    return acc;
  }, {});

  const roleData = role.toJSON();
  const fullRoleInfo = {
    ...roleData,
    parentName: roleData.parentRole?.name || null,
    parentRole: undefined,
    modulePolicies: modulePoliciesByHierarchy,
  };

  return fullRoleInfo;
};

/**
 * Find a role by entity ID and name, excluding a specific ID if provided
 * @param {Object} server - The server instance
 * @param {string} entityId - The entity ID
 * @param {string} name - The role name
 * @param {string|null} excludeId - The role ID to exclude
 * @returns {Promise<Object|null>} - The role if found, null otherwise
 */
export const findByEntityIdAndName = async (server, entityId, name, excludeId = null) => {
  const whereClause = {
    entityId,
    name,
  };

  if (excludeId) {
    whereClause.id = { [server.psql.connection.Sequelize.Op.ne]: excludeId };
  }

  return await server.psql.connection.models.Role.findOne({
    where: whereClause,
  });
};

/**
 * Find all roles by parent ID.
 * @param {Object} server - The server object.
 * @param {string} parentId - The parent role ID.
 * @param {Object} options - Additional options (transaction, etc.).
 * @returns {Promise<Array>} Array of descendant roles.
 */
export const findAllByParentId = async (server, parentId) => {
  return await server.psql.connection.models.Role.findAll({
    where: { parentId },
  });
};

import {
  ModuleRepository,
  PolicySettingRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';
import { clearCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { NAVIGATION_TYPES } from '#src/modules/user/constants/department.constant.js';
import { RoleConstant } from '#src/modules/user/constants/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import { RoleValidation } from '#src/modules/core/validations/index.js';
import { getModulePolicyOptions } from '#src/modules/user/services/department.service.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const POLICIES = RoleConstant.POLICIES;
const {
  MODULE_NAMES: { ROLE },
  MODULE_METHODS: { INDEX },
} = CoreConstant;

const MODULE = ROLE;

/**
 * Retrieves all role entries for a given entity.
 * @param {Object} request - The request object.
 * @returns {Promise<Object[]>} List of role entries.
 */
export const index = async (request) => {
  const { entity } = request;
  const query = {
    ...request.query,
    filter_entityId_eq: entity.id,
  };
  return await RoleRepository.findAll(request.server, query);
};

/**
 * Retrieves a specific role by ID.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The role entry.
 */
export const view = async (request) => {
  const {
    entity,
    params: { id },
    server,
  } = request;

  const role = await RoleRepository.findByIdWithModulePolicies(server, entity.id, id);

  if (!role) {
    throw RoleError.notFound(id);
  }

  return role;
};

/**
 * Creates a new role.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The newly created role.
 * @throws {Error} If creation fails.
 */
export const create = async (request) => {
  const {
    body,
    entity,
    server,
    authInfo: { id: authInfoId },
  } = request;

  const {
    parentId = null,
    departmentId,
    name,
    description = null,
    status = CoreConstant.COMMON_STATUSES.ACTIVE,
    modules = [],
  } = body;

  await RoleValidation.validateRoleName(server, entity.id, name);

  let parentPath = null;
  if (parentId) {
    const parentRole = await RoleRepository.findById(server, parentId);
    if (!parentRole) {
      throw RoleError.parentRoleNotFound(parentId);
    } else {
      parentPath = parentRole.path;
    }
  }

  const filteredModules = await RoleValidation.validateAndFilterModulePolicies(
    server,
    modules,
    entity.hierarchy,
    parentId,
  );

  return withTransaction(server, {}, async (transaction) => {
    const safeLabel = name
      .trim()
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, '_')
      .replace(/_+/g, '_');
    const finalPath = parentPath ? `${parentPath}.${safeLabel}` : safeLabel;
    const newRoleData = {
      parentId,
      entityId: entity.id,
      departmentId,
      name,
      description,
      status,
      path: finalPath,
    };

    const newRole = await RoleRepository.create(server, newRoleData, {
      transaction,
      authInfoId,
    });

    await createRoleModulePolicies(server, newRole.id, filteredModules, authInfoId, transaction);

    const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
    await clearCache(server.redis, cacheKey);

    return newRole;
  });
};

/**
 * Updates an existing role.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The updated role.
 * @throws {Error} If update fails.
 */
export const update = async (request) => {
  const {
    body,
    entity,
    server,
    params,
    authInfo: { id: authInfoId },
  } = request;
  const { id } = params;

  const { name, departmentId, description = null, status, modules = [], version } = body;

  const roleToUpdate = await RoleRepository.findById(server, id);
  if (!roleToUpdate) {
    throw RoleError.notFound(id);
  }
  const originalName = roleToUpdate.name;
  const originalPath = roleToUpdate.path;

  const userRole = await RoleRepository.findById(server, id);
  if (!userRole) {
    throw RoleError.notFound(id);
  }

  if (!RoleValidation.validateHierarchy(userRole.path, roleToUpdate.path)) {
    throw RoleError.notAuthorizedToUpdate();
  }

  await RoleValidation.validateRoleName(server, entity.id, name, id);

  const filteredModules = await RoleValidation.validateAndFilterModulePolicies(
    server,
    modules,
    entity.hierarchy,
    roleToUpdate.parentId,
  );

  return withTransaction(server, {}, async (transaction) => {
    let updatedPath = roleToUpdate.path;
    if (name && name !== originalName) {
      const formattedName = name.replace(/\s+/g, '_');
      updatedPath = updateRolePath(roleToUpdate.path, formattedName);
    }

    const updatedRoleData = {
      name: name || originalName,
      departmentId: departmentId || roleToUpdate.departmentId,
      description,
      status,
      path: updatedPath,
      version,
    };

    const updatedRole = await RoleRepository.update(roleToUpdate, updatedRoleData, {
      transaction,
      authInfoId,
    });

    if (name && name !== originalName) {
      await updateDescendantRolesPaths(
        server,
        entity.id,
        originalPath,
        updatedPath,
        authInfoId,
        transaction,
      );
    }

    await updateRoleModulePolicies(
      server,
      updatedRole.id,
      filteredModules,
      authInfoId,
      transaction,
    );

    return updatedRole;
  });
};

/**
 * Updates the status of a role.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The updated role.
 * @throws {Error} If role is not found or update fails.
 */
export const updateStatus = async (request) => {
  const { body, server, params } = request;
  const { id } = params;
  const { status } = body;

  const roleRow = await RoleRepository.findById(server, id);
  if (!roleRow) {
    throw RoleError.notFound(id);
  }

  return await RoleRepository.update(roleRow, { status }, request.user);
};

/**
 * Retrieves module policy options.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} Module policy options.
 */
export const options = async (request) => await getModulePolicyOptions(request);

/**
 * Retrieves user menus based on role policies.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The user menu structure.
 * @throws {Error} If user role is not found.
 */
export const navigations = async (request) => {
  const { server, authInfo } = request;

  const roleId = authInfo.roleId;
  const userRole = await RoleRepository.findById(server, roleId);
  if (!userRole) {
    throw RoleError.notFound(roleId);
  }

  const allModules = await ModuleRepository.findAll(server);

  const roleModulePolicies = await RoleModuleRepository.findAllByRoleIdWithPolicySettingsAndModules(
    server,
    userRole.id,
  );

  const modulePoliciesMap = roleModulePolicies.reduce((acc, policy) => {
    acc[policy.moduleId] = policy.policySetting;
    return acc;
  }, {});

  const hasViewPolicyInLeaves = (moduleId) => {
    // Get direct child modules
    const childModules = allModules.filter((m) => m.parentId === moduleId);

    // If this is a leaf module (no children), check its own view policy
    if (childModules.length === 0) {
      const policy = modulePoliciesMap[moduleId];
      return policy?.canView;
    }

    // Otherwise, check if any child has view permission
    return childModules.some((child) => hasViewPolicyInLeaves(child.id));
  };

  const getLeafNavigationType = (moduleId) => {
    const childModules = allModules.filter((m) => m.parentId === moduleId);

    // If this is a leaf module, return its navigation type if it has view permission
    if (childModules.length === 0) {
      const policy = modulePoliciesMap[moduleId];
      if (policy?.canView) {
        const module = allModules.find((m) => m.id === moduleId);
        return module.navigationType;
      }
      return null;
    }

    // Otherwise, check children recursively
    for (const child of childModules) {
      const childNavType = getLeafNavigationType(child.id);
      if (childNavType) {
        return childNavType;
      }
    }
    return null;
  };

  const buildMenuStructure = (parentId, hierarchy, navigationType) => {
    // Get modules that match the parent ID and hierarchy
    const modules = allModules.filter((m) => m.parentId === parentId && m.hierarchy === hierarchy);

    return modules.reduce((acc, module) => {
      // Recursively build children menu structure
      const children = buildMenuStructure(module.id, hierarchy, navigationType);

      // Get the navigation type of the first leaf with view permission
      const leafNavType = getLeafNavigationType(module.id);

      // Check if this module or any of its children have view permission
      const hasViewPolicyOrChildren = hasViewPolicyInLeaves(module.id) || children.length > 0;

      if (hasViewPolicyOrChildren) {
        // Include module if it matches the requested navigation type or has children

        if (
          leafNavType === NAVIGATION_TYPES.BOTH ||
          leafNavType === navigationType ||
          children.length > 0
        ) {
          if (children.length > 0) {
            // For modules with children, use module name as key and children as value
            acc.push({ [module.name]: children });
          } else {
            // For leaf modules, include name and URL
            acc.push({
              name: module.name,
              url: module.navigationUrl,
            });
          }
        }
      }

      return acc;
    }, []);
  };

  const menuStructure = {};

  // Get unique hierarchy levels from all modules
  const uniqueHierarchies = [...new Set(allModules.map((m) => m.hierarchy))];

  // Build menu structure for each hierarchy level
  uniqueHierarchies.forEach((hierarchy) => {
    menuStructure[hierarchy] = {};

    // Build separate menu structures for side and top navigation
    const navigationTypes = [NAVIGATION_TYPES.SIDE, NAVIGATION_TYPES.TOP];

    navigationTypes.forEach((navigationType) => {
      menuStructure[hierarchy][navigationType.toLowerCase()] = buildMenuStructure(
        null,
        hierarchy,
        navigationType,
      );
    });
  });

  return menuStructure;
};

const createRoleModulePolicies = async (
  server,
  roleId,
  filteredModules,
  authInfoId,
  transaction,
) => {
  for (const module of filteredModules) {
    const { moduleId, policies: requestedPolicies } = module;

    const [roleModule, created] = await RoleModuleRepository.findOrCreate(
      server,
      {
        roleId: roleId,
        moduleId: moduleId,
      },
      {},
      { transaction, authInfoId },
    );

    if (!created) {
      await RoleModuleRepository.update(roleModule, {}, { transaction, authInfoId });
    }

    const policySettings = Object.fromEntries(requestedPolicies.map((policy) => [policy, true]));

    const [policySetting, policyCreated] = await PolicySettingRepository.findOrCreate(
      server,
      {
        parentId: roleModule.id,
      },
      {
        ...policySettings,
      },
      { transaction, authInfoId },
    );

    if (!policyCreated) {
      await PolicySettingRepository.update(
        policySetting,
        {
          ...policySettings,
        },
        { transaction, authInfoId },
      );
    }
  }
};

/**
 * Updates a role's path.
 * @param {string} oldPath - The original path.
 * @param {string} newName - The new role name.
 * @returns {string} The updated path.
 */
const updateRolePath = (oldPath, newName) => {
  const pathParts = oldPath.split('.');
  pathParts[pathParts.length - 1] = newName;
  return pathParts.join('.');
};

/**
 * Updates paths of descendant roles.
 * @param {Object} server - The server object.
 * @param {string} entityId - The entity ID.
 * @param {string} oldPath - The old path.
 * @param {string} newPath - The new path.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} transaction - The database transaction.
 */
const updateDescendantRolesPaths = async (
  server,
  entityId,
  oldPath,
  newPath,
  authInfoId,
  transaction,
) => {
  const descendantRoles = await RoleRepository.findAllByParentPath(server, entityId, oldPath, {
    transaction,
  });
  for (const descendantRole of descendantRoles) {
    const newDescendantPath = descendantRole.path.replace(oldPath, newPath);
    await RoleRepository.update(
      descendantRole,
      { path: newDescendantPath },
      { transaction, authInfoId },
    );
  }
};

/**
 * Updates role module policies.
 * @param {Object} server - The server object.
 * @param {string} roleId - The role ID.
 * @param {Array} filteredModules - The filtered modules.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} transaction - The database transaction.
 */
const updateRoleModulePolicies = async (
  server,
  roleId,
  filteredModules,
  authInfoId,
  transaction,
) => {
  const existingPolicies = await RoleModuleRepository.findAllByRoleIdWithPolicySettings(
    server,
    roleId,
  );

  const requestedModuleIds = new Set(filteredModules.map(({ moduleId }) => moduleId));

  for (const policy of existingPolicies) {
    if (!requestedModuleIds.has(policy.moduleId)) {
      const allPoliciesFalse = Object.fromEntries(POLICIES.map((p) => [p, false]));

      await updatePolicySettings(server, policy.id, allPoliciesFalse, authInfoId, transaction);
      await updateDescendantPolicies(
        server,
        roleId,
        policy.moduleId,
        allPoliciesFalse,
        authInfoId,
        transaction,
      );
    }
  }

  for (const { moduleId, policies: requestedPolicies } of filteredModules) {
    const [roleModule, created] = await RoleModuleRepository.findOrCreate(
      server,
      { roleId, moduleId },
      {},
      { transaction, authInfoId },
    );

    if (!created) {
      await RoleModuleRepository.update(roleModule, {}, { transaction, authInfoId });
    }

    const allPolicies = Object.fromEntries(POLICIES.map((p) => [p, false]));
    for (const policy of requestedPolicies) {
      allPolicies[policy] = true;
    }

    await updatePolicySettings(server, roleModule.id, allPolicies, authInfoId, transaction);
    await updateDescendantPolicies(server, roleId, moduleId, allPolicies, authInfoId, transaction);
  }
};

/**
 * Updates policies for descendant roles.
 * @param {Object} server - The server object.
 * @param {string} parentRoleId - The parent role ID.
 * @param {string} moduleId - The module ID.
 * @param {Object} parentPolicySettings - The parent policy settings.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} transaction - The database transaction.
 */
const updateDescendantPolicies = async (
  server,
  parentRoleId,
  moduleId,
  parentPolicySettings,
  authInfoId,
  transaction,
) => {
  const descendantRoles = await RoleRepository.findAllByParentId(server, parentRoleId);

  for (const descendantRole of descendantRoles) {
    const [descendantroleModule] = await RoleModuleRepository.findOrCreate(
      server,
      { roleId: descendantRole.id, moduleId },
      {},
      { transaction, authInfoId },
    );

    const descendantPolicySettings = await PolicySettingRepository.findByParentId(
      server,
      descendantroleModule.id,
      { transaction, authInfoId },
    );

    if (descendantPolicySettings) {
      const updatedDescendantPolicySettings = {};
      for (const [policy, value] of Object.entries(descendantPolicySettings.toJSON())) {
        if (POLICIES.includes(policy)) {
          updatedDescendantPolicySettings[policy] =
            value && (parentPolicySettings[policy] || false);
        }
      }

      await updatePolicySettings(
        server,
        descendantroleModule.id,
        updatedDescendantPolicySettings,
        authInfoId,
        transaction,
      );
    }

    await updateDescendantPolicies(
      server,
      descendantRole.id,
      moduleId,
      parentPolicySettings,
      authInfoId,
      transaction,
    );
  }
};

/**
 * Updates policy settings.
 * @param {Object} server - The server object.
 * @param {string} parentId - The parent ID.
 * @param {Object} policySettings - The policy settings to update.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} transaction - The database transaction.
 */
const updatePolicySettings = async (server, parentId, policySettings, authInfoId, transaction) => {
  const allPolicies = POLICIES.reduce((acc, policy) => {
    acc[policy] = false;
    return acc;
  }, {});

  const updatedPolicySettings = {
    ...allPolicies,
    ...policySettings,
  };

  await PolicySettingRepository.upsert(
    server,
    { parentId, ...updatedPolicySettings },
    {
      where: { parentId },
    },
    {
      transaction,
      authInfoId,
    },
  );
};

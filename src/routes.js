import {
  AccessControlRoute,
  DeveloperHubRoute,
  LocalisationRoute,
  SettingRoute,
} from '#src/modules/setting/index.js';
import { DepartmentRoute, DepartmentTemplateRoute, RoleRoute } from '#src/modules/user/index.js';
import { AuditTrailRoute } from '#src/modules/audit-trail/index.js';

import { getPackageJson } from '#src/utils/file.util.js';

// Get the package.json content
const packageJson = getPackageJson();

// Extract the major version number
const majorVersion = packageJson.version.split('.')[0];

// Function to generate prefix
const generatePrefix = (module) => `${API_PREFIX}/v${majorVersion}/${module}`;
const API_PREFIX = '/api';
const routeGroups = [
  {
    routes: AuditTrailRoute,
    opts: { prefix: generatePrefix('audit-trails') },
  },
  {
    routes: DeveloperHubRoute,
    opts: { prefix: generatePrefix('developer-hubs') },
  },
  {
    routes: LocalisationRoute,
    opts: { prefix: generatePrefix('localisations') },
  },
  {
    routes: SettingRoute,
    opts: { prefix: generatePrefix('settings') },
  },
  {
    routes: AccessControlRoute,
    opts: { prefix: generatePrefix('access-controls') },
  },
  {
    routes: DepartmentRoute,
    opts: { prefix: generatePrefix('departments') },
  },
  {
    routes: DepartmentTemplateRoute,
    opts: { prefix: generatePrefix('department-templates') },
  },
  {
    routes: RoleRoute,
    opts: { prefix: generatePrefix('roles') },
  },
];

export default async (fastify, options) => {
  await Promise.all(
    routeGroups.map(async ({ routes, opts }) => {
      await fastify.register(routes, opts);
    }),
  );
};

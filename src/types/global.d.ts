type Language = 'en' | 'zh';

type MenuItem = {
  title: string;
  route?: string;
  icon?: React.ReactNode;
  sortOrder?: number;
  subMenu?: MenuItem[];
};

type Uuid = string;

interface Window {
  google?: {
    accounts: {
      id: {
        initialize: (options: {
          client_id: string;
          callback: (response: { credential: string }) => void;
        }) => void;
        prompt: () => void;
      };
    };
  };
}

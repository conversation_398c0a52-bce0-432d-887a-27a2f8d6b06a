import { findByIdWithModulePolicies } from '#src/modules/user/repository/role.repository.js';
import { roleError } from '#src/modules/user/errors/role.error.js';

export const checkPolicies = async (request, fastify, routeConfig = {}) => {
  const requiredPolicy = routeConfig.policy;

  if (!requiredPolicy) {
    return;
  }

  if (!hasEntityAccess(request)) {
    throw roleError.unauthorizedAccess();
  }

  const [module, action] = requiredPolicy.split('.');

  const hierarchy = request.entity?.hierarchy;
  const roleId = request.authInfo?.roleId;
  const userEntityId = request.userEntity?.id;

  const roleWithPolicies = await findByIdWithModulePolicies(fastify, userEntityId, roleId);

  if (!roleWithPolicies) {
    throw roleError.unauthorizedAccess();
  }

  const hierarchyModules = roleWithPolicies.modulePolicies[hierarchy];

  if (!hierarchyModules) {
    throw roleError.unauthorizedAccess();
  }

  const modulePolicy = hierarchyModules.find(
    (policy) => policy.name.toLowerCase() === module.toLowerCase(),
  );

  if (!modulePolicy?.policySettings?.[action]) {
    throw roleError.unauthorizedAccess();
  }
};

const hasEntityAccess = (request) => {
  const { userEntity, entity, parentEntity } = request;
  if (userEntity.hierarchy === 'root') {
    return true;
  }

  if (userEntity.hierarchy === 'organisation') {
    return (
      (entity.hierarchy === 'organisation' && entity.id === userEntity.id) ||
      (entity.hierarchy === 'merchant' && parentEntity.id === userEntity.id)
    );
  }

  if (userEntity.hierarchy === 'merchant') {
    return entity.id === userEntity.id;
  }

  return false;
};

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { translateMessage } from '#src/utils/i18next.util.js';

const { MODULE_METHODS } = CoreConstant;
const {
  CREATE,
  EXPORT,
  INFO,
  DELETE,
  INDEX,
  OPTION,
  UPDATE,
  UPDATE_ACCESS_CONTROL,
  UPDATE_BASIC_INFORMATION,
  UPDATE_MODULE_POLICY,
  UPDATE_PERSONAL,
  UPDATE_PERMISSION,
  UPDATE_SAFETY,
  UPDATE_STATUS,
  UPDATE_THEMES,
  VIEW,
  NAVIGATION,
} = MODULE_METHODS;
/**
 * Represents a singleton class for generating success messages.
 */
class SuccessMessage {
  /**
   * Creates a new SuccessMessage instance or returns the existing one.
   * Implements the singleton pattern.
   */
  constructor() {
    this.templates = {
      [INDEX]: 'common.sentence.indexSuccess',
      [VIEW]: 'common.sentence.viewSuccess',
      [CREATE]: 'common.sentence.createSuccess',
      [EXPORT]: `common.sentence.exportSuccess`,
      [OPTION]: 'common.sentence.optionSuccess',
      [UPDATE]: 'common.sentence.updateSuccess',
      [UPDATE_STATUS]: 'common.sentence.updateStatusSuccess',
      [UPDATE_BASIC_INFORMATION]: 'common.sentence.updateBasicInformationSuccess',
      [UPDATE_PERSONAL]: 'common.sentence.updatePersonalSuccess',
      [UPDATE_SAFETY]: 'common.sentence.updateSafetySuccess',
      [UPDATE_THEMES]: 'common.sentence.updateThemesSuccess',
      [UPDATE_PERMISSION]: 'common.sentence.updatePermissionsSuccess',
      [UPDATE_ACCESS_CONTROL]: 'common.sentence.updateAccessControlsSuccess',
      [UPDATE_MODULE_POLICY]: 'common.sentence.updateModulePolicySuccess',
      [DELETE]: 'common.sentence.deleteSuccess',
      [NAVIGATION]: 'common.sentence.navigationSuccess',
    };
  }
  static getInstance() {
    if (!SuccessMessage._instance) {
      SuccessMessage._instance = new SuccessMessage();
    }
    return SuccessMessage._instance;
  }

  /**
   * Retrieves a success message template based on the module and operation type.
   * @param {string} module - The name of the module (e.g., 'user', 'product').
   * @param {string} type - The type of operation (e.g., 'view', 'create', 'update', 'updateStatus').
   * @param {...*} opts - Additional optional parameters that might be used in future template expansions.
   * @returns {string} The formatted success message.
   */
  getTemplate(fastify, module, type, ...opts) {
    // Use defined template if available, otherwise use default template.
    const template = this.templates[type];
    if (template) {
      return translateMessage(template, { module: `common.label.${module}` }, fastify);
    }
    return translateMessage('common.sentence.actionSuccess', undefined, fastify); // Fallback on undefined template
  }
}
const instance = SuccessMessage.getInstance();
export default instance;

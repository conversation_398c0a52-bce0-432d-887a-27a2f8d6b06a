import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CoreError } from '#src/modules/core/errors/index.js';
import { applyVersioning } from '#src/mixins/versioned.mixin.js';

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    versionConflict: vi
      .fn()
      .mockImplementation((version) => new Error(`Version conflict: ${version}`)),
  },
}));

describe('Versioned Mixin', () => {
  let mockModel;
  let mockInstance;

  beforeEach(() => {
    mockInstance = {
      previous: vi.fn(),
      get: vi.fn(),
      set: vi.fn(),
    };

    mockModel = {
      beforeUpdate: vi.fn(),
    };

    applyVersioning(mockModel);
  });

  it('should apply beforeUpdate hook', () => {
    expect(mockModel.beforeUpdate).toHaveBeenCalledTimes(1);
  });

  describe('beforeUpdate hook', () => {
    let beforeUpdateHook;

    beforeEach(() => {
      beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
    });

    it('should increment version when versions match', () => {
      mockInstance.previous.mockReturnValue(1);
      mockInstance.get.mockReturnValue(1);

      beforeUpdateHook(mockInstance);

      expect(mockInstance.set).toHaveBeenCalledWith('version', 2);
    });

    it('should throw CoreError.versionConflict when request version is missing', () => {
      mockInstance.previous.mockReturnValue(1);
      mockInstance.get.mockReturnValue(undefined);

      expect(() => beforeUpdateHook(mockInstance)).toThrow('Version conflict: 1');
      expect(CoreError.versionConflict).toHaveBeenCalledWith({ version: 1 });
    });

    it('should throw CoreError.versionConflict when versions do not match', () => {
      mockInstance.previous.mockReturnValue(1);
      mockInstance.get.mockReturnValue(2);

      expect(() => beforeUpdateHook(mockInstance)).toThrow('Version conflict: 1');
      expect(CoreError.versionConflict).toHaveBeenCalledWith({ version: 1 });
    });

    it('should handle string versions correctly', () => {
      mockInstance.previous.mockReturnValue('1');
      mockInstance.get.mockReturnValue('1');

      beforeUpdateHook(mockInstance);

      expect(mockInstance.set).toHaveBeenCalledWith('version', 2);
    });
  });
});

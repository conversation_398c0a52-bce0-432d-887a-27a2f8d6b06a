import { beforeEach, describe, expect, it, vi } from 'vitest';
import { coreError } from '#src/modules/core/errors/core.error.js';

describe('Core Error', () => {
  let mockFastify;

  beforeEach(() => {
    vi.clearAllMocks();

    mockFastify = {
      t: vi.fn().mockImplementation((key) => `t_${key}`),
    };
  });

  describe('unprocessable error', () => {
    it('should create unprocessable error with correct properties', () => {
      const error = coreError.unprocessable();

      expect(error).toBeInstanceOf(Error);
      expect(error.code).toBe('10005');
      expect(error.message).toBe('No changes detected');
      expect(error.statusCode).toBe(422);
      expect(error.name).toBe('coreModuleError');
    });
  });

  it('should have versionConflict error defined', () => {
    expect(coreError.versionConflict).toBeDefined();
    expect(typeof coreError.versionConflict).toBe('function');
  });

  it('should create versionConflict error with correct properties', () => {
    const currentVersion = '5';
    const error = coreError.versionConflict({ version: currentVersion });

    expect(error.code).toEqual('10013');
    expect(error.message).toEqual(`error.sentence.versionConflict`);
    expect(error.statusCode).toEqual(409);
  });

  it('should format versionConflict error message correctly', () => {
    const currentVersion = '5';
    const error = coreError.versionConflict({ version: currentVersion });

    expect(error.tMessageFn(mockFastify)).toBe('t_error.sentence.versionConflict t_5');
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { coreError } from '#src/modules/core/errors/core.error.js';

describe('Core Error', () => {
  let mockFastify;

  beforeEach(() => {
    vi.clearAllMocks();

    mockFastify = {
      t: vi.fn().mockImplementation((key) => `t_${key}`),
    };
  });

  describe('unprocessable error', () => {
    it('should create unprocessable error with correct properties', () => {
      const error = coreError.unprocessable();

      expect(error).toBeInstanceOf(Error);
      expect(error.code).toBe('10015');
      expect(error.message).toBe('error.sentence.unprocessable');
      expect(error.statusCode).toBe(422);
      expect(error.name).toBe('coreModuleError');
    });
  });

  it('should have versionConflict error defined', () => {
    expect(coreError.versionConflict).toBeDefined();
    expect(typeof coreError.versionConflict).toBe('function');
  });

  it('should create versionConflict error with correct properties', () => {
    const currentVersion = '5';
    const error = coreError.versionConflict({ version: currentVersion });

    expect(error.code).toEqual('10013');
    expect(error.message).toEqual(`error.sentence.versionConflict`);
    expect(error.statusCode).toEqual(409);
  });

  it('should format versionConflict error message correctly', () => {
    const currentVersion = '5';
    const error = coreError.versionConflict({ version: currentVersion });

    expect(error.tMessageFn(mockFastify)).toBe('t_error.sentence.versionConflict');
    expect(error.metaData).toEqual({ translationParams: { version: currentVersion } });
  });

  it('should create notFound error with correct properties', () => {
    const error = coreError.notFound();

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('10001');
    expect(error.message).toBe('error.sentence.notFound');
    expect(error.statusCode).toBe(404);
    expect(error.name).toBe('coreModuleError');
  });

  it('should create dataNotFound error with correct properties', () => {
    const error = coreError.dataNotFound();

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('10002');
    expect(error.message).toBe('error.sentence.dataNotFound');
    expect(error.statusCode).toBe(404);
    expect(error.name).toBe('coreModuleError');
  });

  it('should create unauthorised error with correct properties', () => {
    const error = coreError.unauthorised();

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('10003');
    expect(error.message).toBe('error.sentence.unauthorised');
    expect(error.statusCode).toBe(401);
    expect(error.name).toBe('coreModuleError');
  });

  it('should create forbidden error with correct properties', () => {
    const error = coreError.forbidden();

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('10004');
    expect(error.message).toBe('error.sentence.forbidden');
    expect(error.statusCode).toBe(403);
    expect(error.name).toBe('coreModuleError');
  });

  it('should create tooManyRequests error with correct properties', () => {
    const error = coreError.tooManyRequests();

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('10005');
    expect(error.message).toBe('error.sentence.tooManyRequests');
    expect(error.statusCode).toBe(429);
    expect(error.name).toBe('coreModuleError');
  });

  it('should create serviceUnavailable error with correct properties', () => {
    const error = coreError.serviceUnavailable();

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('10016');
    expect(error.message).toBe('error.sentence.serviceUnavailable');
    expect(error.statusCode).toBe(503);
    expect(error.name).toBe('coreModuleError');
  });
});

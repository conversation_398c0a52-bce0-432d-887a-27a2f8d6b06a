import { DataTypes, Model, Sequelize } from 'sequelize';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { RoleConstant } from '#src/modules/user/constants/index.js';
import createRoleModel from '#src/modules/core/models/postgres/role.model.js';

vi.mock('sequelize', () => {
  const actualSequelize = vi.importActual('sequelize');
  return {
    ...actualSequelize,
    DataTypes: {
      UUID: 'UUID',
      ENUM: vi.fn().mockImplementation((...values) => ({ values })),
      STRING: vi.fn().mockReturnValue('STRING'),
      TEXT: 'TEXT',
      VIRTUAL: vi.fn().mockImplementation(() => ({
        type: 'VIRTUAL',
        get: null,
      })),
      BIGINT: 'BIGINT',
    },
    Model: class MockModel {
      static init(attributes) {
        this.attributes = attributes;
        return this;
      }
      static hasMany() {}
      static belongsTo() {}
      static getAttributes() {
        return {
          id: { type: 'UUID', primaryKey: true },
          name: { type: 'STRING', allowNull: false },
          status: {
            type: { values: Object.values(RoleConstant.STATUSES) },
            defaultValue: RoleConstant.STATUSES.ACTIVE,
          },
          parentName: { type: 'VIRTUAL' },
          version: { type: 'BIGINT', defaultValue: 1 },
        };
      }
      static build() {
        const instance = {
          parentRole: null,
          validate: () => Promise.reject(new Error('Validation error')),
        };

        Object.defineProperty(instance, 'parentName', {
          get: function () {
            return this.parentRole?.name;
          },
        });

        return instance;
      }
    },
    Sequelize: {
      literal: vi.fn().mockReturnValue('uuid_generate_v1mc()'),
    },
  };
});

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

describe('Role Model', () => {
  let mockFastify;
  let mockModels;
  let Role;
  let spyInit;
  let spyHasMany;
  let spyBelongsTo;
  let parentNameGetterSpy;

  beforeEach(() => {
    vi.clearAllMocks();

    spyInit = vi.spyOn(Model, 'init');
    spyHasMany = vi.spyOn(Model, 'hasMany');
    spyBelongsTo = vi.spyOn(Model, 'belongsTo');

    mockFastify = {
      psql: {
        connection: {},
      },
    };

    mockModels = {
      RoleModule: {},
      Role: {},
    };

    Role = createRoleModel(mockFastify);
    mockModels.Role = Role;

    parentNameGetterSpy = vi.fn(function () {
      return this.parentRole?.name;
    });

    if (Role.attributes && Role.attributes.parentName) {
      Role.attributes.parentName.get = parentNameGetterSpy;
    }
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize the Role model correctly', () => {
    expect(Role).toBeDefined();
  });

  it('should apply mixins correctly', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(Role);
    expect(versionedMixin.applyVersioning).toHaveBeenCalledWith(Role);
  });

  it('should define the correct attributes', () => {
    expect(spyInit).toHaveBeenCalledWith(
      expect.objectContaining({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING(100),
          allowNull: false,
          validate: {
            len: [1, 100],
            notEmpty: true,
          },
        },
        status: {
          type: DataTypes.ENUM(Object.values(RoleConstant.STATUSES)),
          allowNull: false,
          defaultValue: RoleConstant.STATUSES.ACTIVE,
        },
        parentName: {
          type: DataTypes.VIRTUAL,
          get: expect.any(Function),
        },
        version: {
          type: DataTypes.BIGINT,
          allowNull: false,
          defaultValue: 1,
          validate: {
            min: 1,
            notNull: { msg: 'version is required' },
          },
        },
      }),
      expect.objectContaining({
        modelName: 'Role',
        tableName: 'roles',
        underscored: true,
        timestamps: true,
      }),
    );
  });

  it('should define the correct associations', () => {
    Role.associate(mockModels);

    expect(spyHasMany).toHaveBeenCalledWith(mockModels.RoleModule, {
      foreignKey: 'roleId',
      as: 'modulePolicies',
    });

    expect(spyBelongsTo).toHaveBeenCalledWith(mockModels.Role, {
      as: 'parentRole',
      foreignKey: 'parentId',
    });

    expect(spyHasMany).toHaveBeenCalledWith(mockModels.Role, {
      as: 'childRoles',
      foreignKey: 'parentId',
    });
  });

  it('should define parentName virtual field with correct getter implementation', () => {
    const initCallArgs = spyInit.mock.calls[0][0];
    const parentNameAttr = initCallArgs.parentName;

    expect(parentNameAttr.type).toBe(DataTypes.VIRTUAL);

    const mockInstance = {
      parentRole: { name: 'Test Parent Role' },
    };

    const result = parentNameAttr.get.call(mockInstance);

    expect(result).toBe('Test Parent Role');

    mockInstance.parentRole = null;
    const nullResult = parentNameAttr.get.call(mockInstance);
    expect(nullResult).toBeUndefined();

    mockInstance.parentRole = {};
    const undefinedResult = parentNameAttr.get.call(mockInstance);
    expect(undefinedResult).toBeUndefined();
  });

  it('should validate version field correctly', async () => {
    const roleWithInvalidVersion = Role.build();
    await expect(roleWithInvalidVersion.validate()).rejects.toThrow();
  });

  it('should validate name field correctly', async () => {
    const roleWithEmptyName = Role.build();
    await expect(roleWithEmptyName.validate()).rejects.toThrow();
  });
});

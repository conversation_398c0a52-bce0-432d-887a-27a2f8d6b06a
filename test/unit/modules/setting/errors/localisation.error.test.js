import { describe, expect, it } from 'vitest';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { localisationError } from '#src/modules/setting/errors/localisation.error.js';

describe('Localistion Error Module', () => {
  it('should create inUse error with correct properties', () => {
    const uuid = 'uuid';
    const error = localisationError.inUse({ uuid });

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('51409');
    expect(error.message).toBe(`error.localisations.sentence.inUse`);
    expect(error.statusCode).toBe(409);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.LOCALISATION}ModuleError`);
    expect(error.metaData).toEqual({ translationParams: { uuid } });
  });

  it('should create invalidData error with correct properties', () => {
    const selfDefined = 'custom message';
    const error = localisationError.invalidData(selfDefined);

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('51400');
    expect(error.message).toBe(`custom message`);
    expect(error.statusCode).toBe(400);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.LOCALISATION}ModuleError`);
  });

  it('should create notFound error with correct properties', () => {
    const uuid = 'uuid';
    const error = localisationError.notFound(uuid);

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('51404');
    expect(error.message).toBe(`error.localisations.sentence.notFound ${uuid}`);
    expect(error.statusCode).toBe(404);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.LOCALISATION}ModuleError`);
  });

  it('should create unsupportedUpdate error with correct properties', () => {
    const category = 'currency';
    const error = localisationError.unsupportedUpdate(category);

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('51422');
    expect(error.message).toBe(`error.localisations.sentence.unsupportedUpdate ${category}`);
    expect(error.statusCode).toBe(422);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.LOCALISATION}ModuleError`);
  });
});

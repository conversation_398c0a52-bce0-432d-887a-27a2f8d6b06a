import { beforeEach, describe, expect, it, vi } from 'vitest';
import { findAll, findById } from '#src/modules/user/repository/module.repository.js';

describe('Module Repository', () => {
  const mockFastify = {
    psql: {
      Module: {
        findByPk: vi.fn(),
        findAll: vi.fn(),
      },
    },
  };

  const mockModuleInstance = {
    id: 'module-1',
    name: 'User Management',
    hierarchy: 'admin',
    status: 'active',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findById', () => {
    it('should call findByPk with correct parameters', async () => {
      const moduleId = 'module-1';
      const options = { transaction: 'tx1' };

      mockFastify.psql.Module.findByPk.mockResolvedValue(mockModuleInstance);

      await findById(mockFastify, moduleId, options);

      expect(mockFastify.psql.Module.findByPk).toHaveBeenCalledWith(moduleId, options);
    });

    it('should return the found module', async () => {
      const moduleId = 'module-1';

      mockFastify.psql.Module.findByPk.mockResolvedValue(mockModuleInstance);

      const result = await findById(mockFastify, moduleId);

      expect(result).toBe(mockModuleInstance);
    });

    it('should return null when module is not found', async () => {
      const moduleId = 'non-existent-module';

      mockFastify.psql.Module.findByPk.mockResolvedValue(null);

      const result = await findById(mockFastify, moduleId);

      expect(result).toBeNull();
    });

    it('should use empty options object by default', async () => {
      const moduleId = 'module-1';

      mockFastify.psql.Module.findByPk.mockResolvedValue(mockModuleInstance);

      await findById(mockFastify, moduleId);

      expect(mockFastify.psql.Module.findByPk).toHaveBeenCalledWith(moduleId, {});
    });
  });

  describe('findAll', () => {
    it('should call findAll with correct parameters', async () => {
      const options = {
        where: { status: 'active' },
        transaction: 'tx1',
      };

      const mockModules = [
        mockModuleInstance,
        {
          id: 'module-2',
          name: 'Role Management',
          hierarchy: 'admin',
          status: 'active',
        },
      ];

      mockFastify.psql.Module.findAll.mockResolvedValue(mockModules);

      await findAll(mockFastify, options);

      expect(mockFastify.psql.Module.findAll).toHaveBeenCalledWith(options);
    });

    it('should return all modules', async () => {
      const mockModules = [
        mockModuleInstance,
        {
          id: 'module-2',
          name: 'Role Management',
          hierarchy: 'admin',
          status: 'active',
        },
      ];

      mockFastify.psql.Module.findAll.mockResolvedValue(mockModules);

      const result = await findAll(mockFastify);

      expect(result).toBe(mockModules);
      expect(result.length).toBe(2);
    });

    it('should return empty array when no modules are found', async () => {
      mockFastify.psql.Module.findAll.mockResolvedValue([]);

      const result = await findAll(mockFastify);

      expect(result).toEqual([]);
    });

    it('should use empty options object by default', async () => {
      mockFastify.psql.Module.findAll.mockResolvedValue([mockModuleInstance]);

      await findAll(mockFastify);

      expect(mockFastify.psql.Module.findAll).toHaveBeenCalledWith({});
    });

    it('should handle complex query options', async () => {
      const complexOptions = {
        where: {
          hierarchy: 'admin',
          status: 'active',
        },
        order: [['name', 'ASC']],
        attributes: ['id', 'name', 'hierarchy'],
        limit: 10,
        offset: 0,
      };

      mockFastify.psql.Module.findAll.mockResolvedValue([mockModuleInstance]);

      await findAll(mockFastify, complexOptions);

      expect(mockFastify.psql.Module.findAll).toHaveBeenCalledWith(complexOptions);
    });
  });
});

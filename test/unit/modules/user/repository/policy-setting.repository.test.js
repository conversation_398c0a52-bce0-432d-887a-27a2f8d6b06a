import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  findByParentId,
  findOrCreate,
  upsert,
} from '#src/modules/user/repository/policy-setting.repository.js';

describe('Policy Setting Repository', () => {
  let mockFastify;
  let mockPolicySetting;

  beforeEach(() => {
    vi.resetAllMocks();

    mockPolicySetting = {
      findOne: vi.fn(),
      findOrCreate: vi.fn(),
      upsert: vi.fn(),
    };

    mockFastify = {
      psql: {
        PolicySetting: mockPolicySetting,
      },
    };
  });

  describe('findByParentId', () => {
    it('should call PolicySetting.findOne with correct parameters', async () => {
      const parentId = 'test-parent-id';
      const options = { transaction: 'test-transaction' };
      const expectedPolicySetting = { id: 'policy-1', parentId, read: true, write: false };

      mockPolicySetting.findOne.mockResolvedValue(expectedPolicySetting);

      const result = await findByParentId(mockFastify, parentId, options);

      expect(mockPolicySetting.findOne).toHaveBeenCalledTimes(1);
      expect(mockPolicySetting.findOne).toHaveBeenCalledWith({
        where: { parentId },
        transaction: 'test-transaction',
      });
      expect(result).toEqual(expectedPolicySetting);
    });

    it('should call PolicySetting.findOne with default options when options not provided', async () => {
      const parentId = 'test-parent-id';
      const expectedPolicySetting = { id: 'policy-1', parentId, read: true, write: false };

      mockPolicySetting.findOne.mockResolvedValue(expectedPolicySetting);

      const result = await findByParentId(mockFastify, parentId);

      expect(mockPolicySetting.findOne).toHaveBeenCalledTimes(1);
      expect(mockPolicySetting.findOne).toHaveBeenCalledWith({
        where: { parentId },
      });
      expect(result).toEqual(expectedPolicySetting);
    });

    it('should return null when no policy setting found', async () => {
      const parentId = 'non-existent-parent-id';

      mockPolicySetting.findOne.mockResolvedValue(null);

      const result = await findByParentId(mockFastify, parentId);

      expect(mockPolicySetting.findOne).toHaveBeenCalledTimes(1);
      expect(result).toBeNull();
    });

    it('should propagate errors from the database', async () => {
      const parentId = 'test-parent-id';
      const dbError = new Error('Database error');

      mockPolicySetting.findOne.mockRejectedValue(dbError);

      await expect(findByParentId(mockFastify, parentId)).rejects.toThrow(dbError);
      expect(mockPolicySetting.findOne).toHaveBeenCalledTimes(1);
    });
  });

  describe('findOrCreate', () => {
    it('should call PolicySetting.findOrCreate with correct parameters', async () => {
      const where = { parentId: 'test-parent-id' };
      const defaults = { read: true, write: false };
      const options = { transaction: 'test-transaction' };
      const expectedPolicySetting = [
        { id: 'policy-1', parentId: 'test-parent-id', read: true, write: false },
        true,
      ];

      mockPolicySetting.findOrCreate.mockResolvedValue(expectedPolicySetting);

      const result = await findOrCreate(mockFastify, where, defaults, options);

      expect(mockPolicySetting.findOrCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicySetting.findOrCreate).toHaveBeenCalledWith({
        where,
        defaults,
        transaction: 'test-transaction',
      });
      expect(result).toEqual(expectedPolicySetting);
    });

    it('should call PolicySetting.findOrCreate with default options when options not provided', async () => {
      const where = { parentId: 'test-parent-id' };
      const defaults = { read: true, write: false };
      const expectedPolicySetting = [
        { id: 'policy-1', parentId: 'test-parent-id', read: true, write: false },
        true,
      ];

      mockPolicySetting.findOrCreate.mockResolvedValue(expectedPolicySetting);

      const result = await findOrCreate(mockFastify, where, defaults);

      expect(mockPolicySetting.findOrCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicySetting.findOrCreate).toHaveBeenCalledWith({
        where,
        defaults,
      });
      expect(result).toEqual(expectedPolicySetting);
    });

    it('should propagate errors from the database', async () => {
      const where = { parentId: 'test-parent-id' };
      const defaults = { read: true, write: false };
      const dbError = new Error('Database error');

      mockPolicySetting.findOrCreate.mockRejectedValue(dbError);

      await expect(findOrCreate(mockFastify, where, defaults)).rejects.toThrow(dbError);
      expect(mockPolicySetting.findOrCreate).toHaveBeenCalledTimes(1);
    });
  });

  describe('upsert', () => {
    it('should call PolicySetting.upsert with correct parameters', async () => {
      const values = { parentId: 'test-parent-id', read: true, write: false };
      const options = { transaction: 'test-transaction' };
      const expectedPolicySetting = { id: 'policy-1', ...values };

      mockPolicySetting.upsert.mockResolvedValue([expectedPolicySetting, true]);

      const result = await upsert(mockFastify, values, options);

      expect(mockPolicySetting.upsert).toHaveBeenCalledTimes(1);
      expect(mockPolicySetting.upsert).toHaveBeenCalledWith(values, options);
      expect(result).toEqual([expectedPolicySetting, true]);
    });

    it('should call PolicySetting.upsert with default options when options not provided', async () => {
      const values = { parentId: 'test-parent-id', read: true, write: false };
      const expectedPolicySetting = { id: 'policy-1', ...values };

      mockPolicySetting.upsert.mockResolvedValue([expectedPolicySetting, true]);

      const result = await upsert(mockFastify, values);

      expect(mockPolicySetting.upsert).toHaveBeenCalledTimes(1);
      expect(mockPolicySetting.upsert).toHaveBeenCalledWith(values, {});
      expect(result).toEqual([expectedPolicySetting, true]);
    });

    it('should propagate errors from the database', async () => {
      const values = { parentId: 'test-parent-id', read: true, write: false };
      const dbError = new Error('Database error');

      mockPolicySetting.upsert.mockRejectedValue(dbError);

      await expect(upsert(mockFastify, values)).rejects.toThrow(dbError);
      expect(mockPolicySetting.upsert).toHaveBeenCalledTimes(1);
    });
  });
});

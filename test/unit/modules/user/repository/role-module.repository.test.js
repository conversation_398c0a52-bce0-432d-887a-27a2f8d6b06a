import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  findAllByRoleId,
  findAllByRoleIdWithPolicySettings,
  findAllByRoleIdWithPolicySettingsAndModules,
  findOrCreate,
  update,
} from '#src/modules/user/repository/role-module.repository.js';

describe('Role Module Repository', () => {
  const mockFastify = {
    psql: {
      RoleModule: {
        findOrCreate: vi.fn(),
        findAll: vi.fn(),
      },
      Module: {},
      PolicySetting: {},
    },
  };

  const mockServer = {
    psql: {
      connection: {
        models: {
          RoleModule: {
            findAll: vi.fn(),
          },
          PolicySetting: {},
        },
      },
    },
  };

  const mockRoleModuleInstance = {
    id: 'role-module-1',
    roleId: 'role-1',
    moduleId: 'module-1',
    update: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findOrCreate', () => {
    it('should call findOrCreate with correct parameters', async () => {
      const where = { roleId: 'role-1', moduleId: 'module-1' };
      const defaults = { status: 'active' };
      const options = { transaction: 'tx1' };

      mockFastify.psql.RoleModule.findOrCreate.mockResolvedValue([mockRoleModuleInstance, true]);

      await findOrCreate(mockFastify, where, defaults, options);

      expect(mockFastify.psql.RoleModule.findOrCreate).toHaveBeenCalledWith({
        where,
        defaults,
        transaction: 'tx1',
      });
    });

    it('should return the result of findOrCreate', async () => {
      const where = { roleId: 'role-1', moduleId: 'module-1' };
      const defaults = { status: 'active' };
      const expectedResult = [mockRoleModuleInstance, true];

      mockFastify.psql.RoleModule.findOrCreate.mockResolvedValue(expectedResult);

      const result = await findOrCreate(mockFastify, where, defaults);

      expect(result).toEqual(expectedResult);
    });

    it('should use empty options object by default', async () => {
      const where = { roleId: 'role-1', moduleId: 'module-1' };
      const defaults = { status: 'active' };

      mockFastify.psql.RoleModule.findOrCreate.mockResolvedValue([mockRoleModuleInstance, true]);

      await findOrCreate(mockFastify, where, defaults);

      expect(mockFastify.psql.RoleModule.findOrCreate).toHaveBeenCalledWith({
        where,
        defaults,
      });
    });
  });

  describe('findAllByRoleId', () => {
    it('should call findAll with correct parameters', async () => {
      const roleId = 'role-1';
      const options = { transaction: 'tx1' };

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([mockRoleModuleInstance]);

      await findAllByRoleId(mockFastify, roleId, options);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
        transaction: 'tx1',
      });
    });

    it('should return the result of findAll', async () => {
      const roleId = 'role-1';
      const expectedResult = [mockRoleModuleInstance];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedResult);

      const result = await findAllByRoleId(mockFastify, roleId);

      expect(result).toEqual(expectedResult);
    });

    it('should use empty options object by default', async () => {
      const roleId = 'role-1';

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([mockRoleModuleInstance]);

      await findAllByRoleId(mockFastify, roleId);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
      });
    });
  });

  describe('update', () => {
    it('should call update with correct parameters', async () => {
      const data = { status: 'inactive' };
      const options = { transaction: 'tx1' };
      const expectedResult = { id: 'role-module-1', updated: true };

      mockRoleModuleInstance.update.mockResolvedValue(expectedResult);

      await update(mockRoleModuleInstance, data, options);

      expect(mockRoleModuleInstance.update).toHaveBeenCalledWith(data, options);
    });

    it('should return the result of update', async () => {
      const data = { status: 'inactive' };
      const expectedResult = { id: 'role-module-1', updated: true };

      mockRoleModuleInstance.update.mockResolvedValue(expectedResult);

      const result = await update(mockRoleModuleInstance, data);

      expect(result).toEqual(expectedResult);
    });

    it('should use empty options object by default', async () => {
      const data = { status: 'inactive' };

      mockRoleModuleInstance.update.mockResolvedValue({ id: 'role-module-1', updated: true });

      await update(mockRoleModuleInstance, data);

      expect(mockRoleModuleInstance.update).toHaveBeenCalledWith(data, {});
    });
  });

  describe('findAllByRoleIdWithPolicySettings', () => {
    it('should call findAll with correct parameters', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policySetting: {
            id: 'policy-1',
            key: 'canView',
            value: true,
          },
        },
      ];

      mockServer.psql.connection.models.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      await findAllByRoleIdWithPolicySettings(mockServer, roleId);

      expect(mockServer.psql.connection.models.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
        include: [
          {
            model: mockServer.psql.connection.models.PolicySetting,
            as: 'policySetting',
          },
        ],
      });
    });

    it('should return the result of findAll with policy settings', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policySetting: {
            id: 'policy-1',
            key: 'canView',
            value: true,
          },
        },
      ];

      mockServer.psql.connection.models.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      const result = await findAllByRoleIdWithPolicySettings(mockServer, roleId);

      expect(result).toEqual(expectedRoleModules);
    });
  });

  describe('findAllByRoleIdWithPolicySettingsAndModules', () => {
    it('should call findAll with correct parameters', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policySetting: {
            id: 'policy-1',
            canView: true,
            canEdit: false,
          },
          module: {
            id: 'module-1',
            name: 'User Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      await findAllByRoleIdWithPolicySettingsAndModules(mockFastify, roleId);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
        include: [
          {
            model: mockFastify.psql.PolicySetting,
            as: 'policySetting',
          },
          {
            model: mockFastify.psql.Module,
            as: 'module',
          },
        ],
      });
    });

    it('should return the result of findAll with policy settings and modules', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policySetting: {
            id: 'policy-1',
            canView: true,
            canEdit: false,
          },
          module: {
            id: 'module-1',
            name: 'User Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
        {
          id: 'role-module-2',
          roleId: 'role-1',
          moduleId: 'module-2',
          policySetting: {
            id: 'policy-2',
            canView: true,
            canEdit: true,
          },
          module: {
            id: 'module-2',
            name: 'Role Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      const result = await findAllByRoleIdWithPolicySettingsAndModules(mockFastify, roleId);

      expect(result).toEqual(expectedRoleModules);
      expect(result.length).toBe(2);
    });

    it('should return empty array when no role modules are found', async () => {
      const roleId = 'non-existent-role';

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([]);

      const result = await findAllByRoleIdWithPolicySettingsAndModules(mockFastify, roleId);

      expect(result).toEqual([]);
      expect(result.length).toBe(0);
    });

    it('should handle role modules with missing policy settings or modules', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policySetting: null,
          module: {
            id: 'module-1',
            name: 'User Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
        {
          id: 'role-module-2',
          roleId: 'role-1',
          moduleId: 'module-2',
          policySetting: {
            id: 'policy-2',
            canView: true,
            canEdit: true,
          },
          module: null,
        },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      const result = await findAllByRoleIdWithPolicySettingsAndModules(mockFastify, roleId);

      expect(result).toEqual(expectedRoleModules);
      expect(result[0].policySetting).toBeNull();
      expect(result[1].module).toBeNull();
    });
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  create,
  findAll,
  findAllByParentId,
  findAllByParentPath,
  findByEntityIdAndName,
  findById,
  findByIdWithModulePolicies,
  update,
  updateStatus,
} from '#src/modules/user/repository/role.repository.js';
import { Sequelize } from 'sequelize';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/pagination.util.js', () => ({
  default: vi.fn(),
}));

vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn(),
}));

vi.mock('#src/utils/pagination.util.js', () => ({
  applyOffsetPagination: vi.fn(),
}));

describe('Role Repository', () => {
  const mockFastify = {
    psql: {
      Role: {
        findByPk: vi.fn(),
        findAll: vi.fn(),
        create: vi.fn(),
      },
      connection: {
        models: {
          Role: {
            findOne: vi.fn(),
            findAll: vi.fn(),
          },
          PolicySetting: {},
          Module: {},
        },
        Sequelize: {
          Op: {
            or: Symbol('or'),
            ne: Symbol('ne'),
          },
          literal: vi.fn((str) => `LITERAL(${str})`),
        },
      },
    },
  };

  const mockRoleInstance = {
    toJSON: vi.fn(() => ({
      id: 1,
      name: 'Test Role',
      parentRole: { name: 'Parent Role' },
    })),
    update: vi.fn().mockResolvedValue({ id: 1, updated: true }),
    getModulePolicies: vi.fn(),
  };

  const mockQuery = {
    page: 1,
    limit: 10,
    sortBy: [{ field: 'name', direction: 'asc' }],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('should call buildWhereFromFilters with correct parameters', async () => {
      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      buildWhereFromFilters.mockReturnValue({
        where: { status: 'active' },
        include: [{ model: 'ParentRole' }],
      });

      applyOffsetPagination.mockResolvedValue({
        rows: [{ id: 1, name: 'Role 1' }],
        pagination: { totalCount: 1 },
      });

      await findAll(mockFastify, mockQuery);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(
        mockQuery,
        mockFastify.psql.Role,
        expectedIncludes,
      );
    });

    it('should call applyOffsetPagination with correct parameters', async () => {
      const mockWhereFilter = { status: 'active' };
      const mockIncludeFilter = [{ model: 'ParentRole' }];

      buildWhereFromFilters.mockReturnValue({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });

      const expectedResult = {
        rows: [{ id: 1, name: 'Role 1' }],
        pagination: { totalCount: 1 },
      };

      applyOffsetPagination.mockResolvedValue(expectedResult);

      const result = await findAll(mockFastify, mockQuery);

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.Role,
        mockQuery,
        mockWhereFilter,
        mockIncludeFilter,
        ['id', 'name', 'status', 'createdAt', 'updatedAt'],
      );

      expect(result).toEqual(expectedResult);
    });
  });

  describe('findById', () => {
    it('should call findByPk with correct parameters', async () => {
      const roleId = 1;
      mockFastify.psql.Role.findByPk.mockResolvedValue(mockRoleInstance);

      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      await findById(mockFastify, roleId);

      expect(mockFastify.psql.Role.findByPk).toHaveBeenCalledWith(roleId, {
        include: expectedIncludes,
      });
    });

    it('should return the found role', async () => {
      const roleId = 1;
      mockFastify.psql.Role.findByPk.mockResolvedValue(mockRoleInstance);

      const result = await findById(mockFastify, roleId);

      expect(result).toBe(mockRoleInstance);
    });
  });

  describe('findAllByParentPath', () => {
    it('should call findAll with correct ltree query', async () => {
      const entityId = 'entity-1';
      const parentPath = 'root.admin';
      const options = { transaction: 'tx1' };

      mockFastify.psql.Role.findAll.mockResolvedValue([mockRoleInstance]);

      await findAllByParentPath(mockFastify, entityId, parentPath, options);

      expect(mockFastify.psql.Role.findAll).toHaveBeenCalledWith({
        where: {
          entityId: entityId,
          [Sequelize.Op.or]: [
            Sequelize.literal(`"path" = '${parentPath}'::ltree`),
            Sequelize.literal(`"path" <@ '${parentPath}'::ltree`),
          ],
        },
        ...options,
      });
    });

    it('should return the found roles', async () => {
      const entityId = 'entity-1';
      const parentPath = 'root.admin';

      mockFastify.psql.Role.findAll.mockResolvedValue([mockRoleInstance]);

      const result = await findAllByParentPath(mockFastify, entityId, parentPath);

      expect(result).toEqual([mockRoleInstance]);
    });
  });

  describe('create', () => {
    it('should call create with correct parameters', async () => {
      const roleData = {
        name: 'New Role',
        entityId: 'entity-1',
        status: 'active',
      };
      const options = { transaction: 'tx1' };

      mockFastify.psql.Role.create.mockResolvedValue(mockRoleInstance);

      await create(mockFastify, roleData, options);

      expect(mockFastify.psql.Role.create).toHaveBeenCalledWith(roleData, options);
    });

    it('should return the created role as JSON', async () => {
      const roleId = 'creator-1';
      const roleData = {
        name: 'New Role',
        entityId: 'entity-1',
        status: 'active',
      };

      const expectedResult = {
        id: 1,
        name: 'Test Role',
        parentRole: { name: 'Parent Role' },
      };

      mockRoleInstance.toJSON.mockReturnValue(expectedResult);
      mockFastify.psql.Role.create.mockResolvedValue(mockRoleInstance);

      const result = await create(mockFastify, roleId, roleData);

      expect(mockRoleInstance.toJSON).toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });
  });

  describe('update', () => {
    it('should call update with correct parameters', async () => {
      const updateData = {
        name: 'Updated Role',
        status: 'inactive',
      };
      const options = { transaction: 'tx1' };

      await update(mockRoleInstance, updateData, options);

      expect(mockRoleInstance.update).toHaveBeenCalledWith(updateData, options);
    });

    it('should return the update result', async () => {
      const updateData = {
        name: 'Updated Role',
      };
      const expectedResult = { id: 1, updated: true };

      mockRoleInstance.update.mockResolvedValue(expectedResult);

      const result = await update(mockRoleInstance, updateData);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('updateStatus', () => {
    it('should call update with status parameter', async () => {
      const status = 'inactive';
      const options = { transaction: 'tx1' };

      await updateStatus(mockRoleInstance, status, options);

      expect(mockRoleInstance.update).toHaveBeenCalledWith({ status }, options);
    });

    it('should return the update result', async () => {
      const status = 'inactive';
      const expectedResult = { id: 1, updated: true };

      mockRoleInstance.update.mockResolvedValue(expectedResult);

      const result = await updateStatus(mockRoleInstance, status);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByIdWithModulePolicies', () => {
    it('should return null if role not found', async () => {
      const entityId = 'entity-1';
      const roleId = 1;

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(null);

      const result = await findByIdWithModulePolicies(mockFastify, entityId, roleId);

      expect(result).toBeNull();
    });

    it('should return role with module policies grouped by hierarchy', async () => {
      const entityId = 'entity-1';
      const roleId = 1;

      const mockRole = {
        toJSON: vi.fn(() => ({
          id: roleId,
          name: 'Admin Role',
          parentRole: { name: 'Super Admin' },
        })),
        getModulePolicies: vi.fn(),
      };

      const mockModulePolicies = [
        {
          moduleId: 'module-1',
          module: {
            name: 'User Management',
            hierarchy: 'admin',
          },
          policySetting: {
            toJSON: () => ({
              id: 101,
              parentId: null,
              key: 'canView',
              value: true,
              createdAt: '2023-01-01',
              updatedAt: '2023-01-01',
              createdBy: 'system',
              updatedBy: 'system',
            }),
          },
        },
        {
          moduleId: 'module-2',
          module: {
            name: 'Role Management',
            hierarchy: 'admin',
          },
          policySetting: {
            toJSON: () => ({
              id: 102,
              parentId: null,
              key: 'canEdit',
              value: true,
              createdAt: '2023-01-01',
              updatedAt: '2023-01-01',
              createdBy: 'system',
              updatedBy: 'system',
            }),
          },
        },
        {
          moduleId: 'module-3',
          module: {
            name: 'Profile',
            hierarchy: 'user',
          },
          policySetting: {
            toJSON: () => ({
              id: 103,
              parentId: null,
              key: 'canView',
              value: true,
              createdAt: '2023-01-01',
              updatedAt: '2023-01-01',
              createdBy: 'system',
              updatedBy: 'system',
            }),
          },
        },
        {
          moduleId: 'module-4',
          module: {
            name: 'Settings',
            hierarchy: 'user',
          },
          policySetting: null,
        },
      ];

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRole);
      mockRole.getModulePolicies.mockResolvedValue(mockModulePolicies);

      const result = await findByIdWithModulePolicies(mockFastify, entityId, roleId);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: { id: roleId, entityId },
        include: [
          {
            association: 'parentRole',
            attributes: ['name'],
            required: false,
          },
        ],
      });

      expect(mockRole.getModulePolicies).toHaveBeenCalledWith({
        include: [
          {
            model: mockFastify.psql.connection.models.PolicySetting,
            as: 'policySetting',
          },
          {
            model: mockFastify.psql.connection.models.Module,
            as: 'module',
          },
        ],
      });

      expect(result).toEqual({
        id: roleId,
        name: 'Admin Role',
        parentName: 'Super Admin',
        parentRole: undefined,
        modulePolicies: {
          admin: [
            {
              id: 'module-1',
              name: 'User Management',
              policySettings: {
                key: 'canView',
                value: true,
              },
            },
            {
              id: 'module-2',
              name: 'Role Management',
              policySettings: {
                key: 'canEdit',
                value: true,
              },
            },
          ],
          user: [
            {
              id: 'module-3',
              name: 'Profile',
              policySettings: {
                key: 'canView',
                value: true,
              },
            },
            {
              id: 'module-4',
              name: 'Settings',
              policySettings: {},
            },
          ],
        },
      });
    });

    it('should handle role with no parent role', async () => {
      const entityId = 'entity-1';
      const roleId = 1;

      const mockRole = {
        toJSON: vi.fn(() => ({
          id: roleId,
          name: 'Root Role',
        })),
        getModulePolicies: vi.fn().mockResolvedValue([]),
      };

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRole);

      const result = await findByIdWithModulePolicies(mockFastify, entityId, roleId);

      expect(result).toEqual({
        id: roleId,
        name: 'Root Role',
        parentName: null,
        parentRole: undefined,
        modulePolicies: {},
      });
    });
  });

  describe('findByEntityIdAndName', () => {
    it('should call findOne with correct parameters without excludeId', async () => {
      const entityId = 'entity-1';
      const name = 'Admin Role';

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRoleInstance);

      await findByEntityIdAndName(mockFastify, entityId, name);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: {
          entityId,
          name,
        },
      });
    });

    it('should call findOne with correct parameters with excludeId', async () => {
      const entityId = 'entity-1';
      const name = 'Admin Role';
      const excludeId = 5;

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRoleInstance);

      await findByEntityIdAndName(mockFastify, entityId, name, excludeId);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: {
          entityId,
          name,
          id: { [mockFastify.psql.connection.Sequelize.Op.ne]: excludeId },
        },
      });
    });

    it('should return the found role', async () => {
      const entityId = 'entity-1';
      const name = 'Admin Role';

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRoleInstance);

      const result = await findByEntityIdAndName(mockFastify, entityId, name);

      expect(result).toBe(mockRoleInstance);
    });
  });

  describe('findAllByParentId', () => {
    it('should call findAll with correct parameters', async () => {
      const parentId = 1;
      const mockRoles = [mockRoleInstance, { ...mockRoleInstance, id: 2 }];

      mockFastify.psql.connection.models.Role.findAll.mockResolvedValue(mockRoles);

      await findAllByParentId(mockFastify, parentId);

      expect(mockFastify.psql.connection.models.Role.findAll).toHaveBeenCalledWith({
        where: { parentId },
      });
    });

    it('should return the found roles', async () => {
      const parentId = 1;
      const mockRoles = [mockRoleInstance, { ...mockRoleInstance, id: 2 }];

      mockFastify.psql.connection.models.Role.findAll.mockResolvedValue(mockRoles);

      const result = await findAllByParentId(mockFastify, parentId);

      expect(result).toBe(mockRoles);
    });
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { RoleHandler } from '#src/modules/user/handlers/index.js';
import { RoleSchema } from '#src/modules/user/schemas/index.js';

import RoleRoute from '#src/modules/user/routes/role.route.js';

describe('Role Route', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
    };

    vi.resetAllMocks();
  });

  it('should register all routes with correct configurations', async () => {
    await RoleRoute(fastifyMock, {});

    const expectedAccessConfig = {
      user: true,
      member: false,
      webhook: false,
      public: false,
      ipWhitelist: ['127.0.0.1'],
    };

    expect(fastifyMock.get).toHaveBeenCalledWith('/', {
      schema: RoleSchema.index,
      config: { name: 'role.list', policy: 'roles.canView', access: expectedAccessConfig },
      handler: RoleHandler.index,
    });

    expect(fastifyMock.get).toHaveBeenCalledWith('/:id', {
      schema: RoleSchema.view,
      config: { name: 'role.view', policy: 'roles.canView', access: expectedAccessConfig },
      handler: RoleHandler.view,
    });

    expect(fastifyMock.post).toHaveBeenCalledWith('/', {
      schema: RoleSchema.create,
      config: { name: 'role.create', policy: 'roles.canCreate', access: expectedAccessConfig },
      handler: RoleHandler.create,
    });

    expect(fastifyMock.put).toHaveBeenCalledWith('/:id', {
      schema: RoleSchema.update,
      config: { name: 'role.update', policy: 'roles.canEdit', access: expectedAccessConfig },
      handler: RoleHandler.update,
    });

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/status', {
      schema: RoleSchema.updateStatus,
      config: {
        name: 'role.updateStatus',
        policy: 'roles.canManage',
        access: expectedAccessConfig,
      },
      handler: RoleHandler.updateStatus,
    });

    expect(fastifyMock.get).toHaveBeenCalledWith('/options', {
      schema: RoleSchema.options,
      config: { name: 'role.options', policy: 'roles.canView', access: expectedAccessConfig },
      handler: RoleHandler.options,
    });

    expect(fastifyMock.get).toHaveBeenCalledWith('/navigations', {
      schema: RoleSchema.navigations,
      config: { name: 'role.navigations', access: expectedAccessConfig },
      handler: RoleHandler.navigations,
    });
  });

  it('should register the correct number of routes', async () => {
    await RoleRoute(fastifyMock, {});

    const totalRoutes =
      fastifyMock.get.mock.calls.length +
      fastifyMock.post.mock.calls.length +
      fastifyMock.put.mock.calls.length +
      fastifyMock.patch.mock.calls.length;

    expect(totalRoutes).toBe(7);
    expect(fastifyMock.get.mock.calls.length).toBe(4);
    expect(fastifyMock.post.mock.calls.length).toBe(1);
    expect(fastifyMock.put.mock.calls.length).toBe(1);
    expect(fastifyMock.patch.mock.calls.length).toBe(1);
  });

  it('should use the correct handlers for each route', async () => {
    await RoleRoute(fastifyMock, {});

    const getIndexRoute = fastifyMock.get.mock.calls.find((call) => call[0] === '/');
    expect(getIndexRoute[1].handler).toBe(RoleHandler.index);

    const getViewRoute = fastifyMock.get.mock.calls.find((call) => call[0] === '/:id');
    expect(getViewRoute[1].handler).toBe(RoleHandler.view);

    const postCreateRoute = fastifyMock.post.mock.calls.find((call) => call[0] === '/');
    expect(postCreateRoute[1].handler).toBe(RoleHandler.create);

    const putUpdateRoute = fastifyMock.put.mock.calls.find((call) => call[0] === '/:id');
    expect(putUpdateRoute[1].handler).toBe(RoleHandler.update);

    const patchStatusRoute = fastifyMock.patch.mock.calls.find((call) => call[0] === '/:id/status');
    expect(patchStatusRoute[1].handler).toBe(RoleHandler.updateStatus);
  });

  it('should use the correct schemas for each route', async () => {
    await RoleRoute(fastifyMock, {});

    const getIndexRoute = fastifyMock.get.mock.calls.find((call) => call[0] === '/');
    expect(getIndexRoute[1].schema).toBe(RoleSchema.index);

    const getViewRoute = fastifyMock.get.mock.calls.find((call) => call[0] === '/:id');
    expect(getViewRoute[1].schema).toBe(RoleSchema.view);

    const postCreateRoute = fastifyMock.post.mock.calls.find((call) => call[0] === '/');
    expect(postCreateRoute[1].schema).toBe(RoleSchema.create);

    const putUpdateRoute = fastifyMock.put.mock.calls.find((call) => call[0] === '/:id');
    expect(putUpdateRoute[1].schema).toBe(RoleSchema.update);

    const patchStatusRoute = fastifyMock.patch.mock.calls.find((call) => call[0] === '/:id/status');
    expect(patchStatusRoute[1].schema).toBe(RoleSchema.updateStatus);
  });

  it('should use consistent access configuration for all routes', async () => {
    await RoleRoute(fastifyMock, {});

    const expectedAccessConfig = {
      user: true,
      member: false,
      webhook: false,
      public: false,
      ipWhitelist: ['127.0.0.1'],
    };

    const allRoutes = [
      ...fastifyMock.get.mock.calls,
      ...fastifyMock.post.mock.calls,
      ...fastifyMock.put.mock.calls,
      ...fastifyMock.patch.mock.calls,
    ];

    allRoutes.forEach((route) => {
      expect(route[1].config.access).toEqual(expectedAccessConfig);
    });
  });
});

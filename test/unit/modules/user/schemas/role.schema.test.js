import { describe, expect, it } from 'vitest';
import { CoreSchema } from '#src/modules/core/schemas/index.js';

import { COMMON_STATUSES, MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';

import * as roleSchema from '#src/modules/user/schemas/role.schema.js';

describe('Role Schema Module', () => {
  const { ROLE } = MODULE_NAMES;
  const TAGS = ['BO / User Management / Roles'];

  it('should have correct tags and summary for index schema', () => {
    expect(roleSchema.index.tags).toEqual(TAGS);
    expect(roleSchema.index.summary).toBe(`Get a list of ${ROLE}`);
  });

  it('should have correct query parameters for index schema', () => {
    const querystring = roleSchema.index.querystring;
    expect(querystring.type).toBe('object');
    expect(querystring.properties).toHaveProperty('filter_name_eq');
    expect(querystring.properties).toHaveProperty('filter_parentId_eq');
    expect(querystring.properties).toHaveProperty('filter_departmentId_eq');
    expect(querystring.properties).toHaveProperty('filter_status_eq');
  });

  it('should have correct response schema for index', () => {
    const response = roleSchema.index.response;
    expect(response).toHaveProperty('200');
    expect(response['200'].type).toBe('object');
    expect(response['200'].properties).toHaveProperty('message');
    expect(response['200'].properties).toHaveProperty('data');
    expect(response['200'].properties).toHaveProperty('meta');
    expect(response['200'].properties.data.type).toBe('array');
  });

  it('should have correct tags and summary for view schema', () => {
    expect(roleSchema.view.tags).toEqual(TAGS);
    expect(roleSchema.view.summary).toBe(`View a ${ROLE}`);
  });

  it('should have correct params for view schema', () => {
    expect(roleSchema.view.params).toEqual(CoreSchema.REQ_PARAM_UUID);
  });

  it('should have correct response schema for view', () => {
    const response = roleSchema.view.response;
    expect(response).toHaveProperty('200');
    expect(response['200'].properties.data.properties).toHaveProperty('name');
    expect(response['200'].properties.data.properties).toHaveProperty('description');
    expect(response['200'].properties.data.properties).toHaveProperty('status');
    expect(response['200'].properties.data.properties).toHaveProperty('parentName');
    expect(response['200'].properties.data.properties).toHaveProperty('modulePolicies');
    expect(response['200'].properties.data.properties).toHaveProperty('metadata');
    expect(response['200'].properties.data.properties).toHaveProperty('version');
  });

  it('should have correct tags and summary for create schema', () => {
    expect(roleSchema.create.tags).toEqual(TAGS);
    expect(roleSchema.create.summary).toBe(`Create a ${ROLE}`);
  });

  it('should have correct body schema for create', () => {
    const body = roleSchema.create.body;
    expect(body.type).toBe('object');
    expect(body.properties).toHaveProperty('name');
    expect(body.properties).toHaveProperty('description');
    expect(body.properties).toHaveProperty('parentId');
    expect(body.properties).toHaveProperty('departmentId');
    expect(body.properties).toHaveProperty('modules');
    expect(body.required).toContain('name');
    expect(body.required).toContain('modules');
  });

  it('should have correct response schema for create', () => {
    expect(roleSchema.create.response).toEqual(CoreSchema.CREATE_RESPONSE);
  });

  it('should have correct tags and summary for update schema', () => {
    expect(roleSchema.update.tags).toEqual(TAGS);
    expect(roleSchema.update.summary).toBe(`Update a ${ROLE}`);
  });

  it('should have correct params for update schema', () => {
    expect(roleSchema.update.params).toEqual(CoreSchema.REQ_PARAM_UUID);
  });

  it('should have correct body schema for update', () => {
    const body = roleSchema.update.body;
    expect(body.type).toBe('object');
    expect(body.properties).toHaveProperty('name');
    expect(body.properties).toHaveProperty('description');
    expect(body.properties).toHaveProperty('modules');
    expect(body.properties).toHaveProperty('version');
    expect(body.required).toContain('name');
    expect(body.required).toContain('modules');
    expect(body.required).toContain('version');
  });

  it('should have correct response schema for update', () => {
    expect(roleSchema.update.response).toEqual(CoreSchema.UPDATE_RESPONSE);
  });

  it('should have correct tags and summary for updateStatus schema', () => {
    expect(roleSchema.updateStatus.tags).toEqual(TAGS);
    expect(roleSchema.updateStatus.summary).toBe(`Update a ${ROLE} status`);
  });

  it('should have correct params for updateStatus schema', () => {
    expect(roleSchema.updateStatus.params).toEqual(CoreSchema.REQ_PARAM_UUID);
  });

  it('should have correct body schema for updateStatus', () => {
    const body = roleSchema.updateStatus.body;
    expect(body.type).toBe('object');
    expect(body.properties).toHaveProperty('status');
    expect(body.properties).toHaveProperty('version');
    expect(body.properties.status.enum).toEqual(Object.values(COMMON_STATUSES));
    expect(body.required).toContain('status');
    expect(body.required).toContain('version');
  });

  it('should have correct response schema for updateStatus', () => {
    expect(roleSchema.updateStatus.response).toEqual(CoreSchema.UPDATE_RESPONSE);
  });

  it('should define ROLE_RES_PROPERTIES with all required fields', () => {
    const response = roleSchema.view.response;
    const properties = response['200'].properties.data.properties;

    Object.keys(CoreSchema.COMMON_PROPERTIES).forEach((key) => {
      expect(properties).toHaveProperty(key);
    });

    expect(properties).toHaveProperty('name');
    expect(properties).toHaveProperty('description');
    expect(properties).toHaveProperty('status');
    expect(properties.status.enum).toEqual(Object.values(COMMON_STATUSES));
    expect(properties).toHaveProperty('parentName');
    expect(properties).toHaveProperty('modulePolicies');
    expect(properties.modulePolicies.properties).toHaveProperty('root');
    expect(properties.modulePolicies.properties).toHaveProperty('organisation');
    expect(properties.modulePolicies.properties).toHaveProperty('merchant');
    expect(properties).toHaveProperty('metadata');
    expect(properties).toHaveProperty('version');
  });

  it('should have correct format for UUID fields', () => {
    expect(roleSchema.create.body.properties.parentId.format).toBe('uuid');
    expect(roleSchema.create.body.properties.departmentId.format).toBe('uuid');
  });

  it('should have correct tags and summary for navigations schema', () => {
    expect(roleSchema.navigations.tags).toEqual(TAGS);
    expect(roleSchema.navigations.summary).toBe('Get user navigation menus based on role policies');
  });

  it('should have correct response schema for navigations', () => {
    const response = roleSchema.navigations.response;
    expect(response).toHaveProperty('200');
    expect(response['200'].type).toBe('object');
    expect(response['200'].properties).toHaveProperty('message');
    expect(response['200'].properties).toHaveProperty('data');
    expect(response['200'].properties).toHaveProperty('debug');

    const data = response['200'].properties.data;
    expect(data.type).toBe('object');
    expect(data.properties).toHaveProperty('root');
    expect(data.properties).toHaveProperty('organisation');
    expect(data.properties).toHaveProperty('merchant');

    ['root', 'organisation', 'merchant'].forEach((level) => {
      expect(data.properties[level].type).toBe('object');
      expect(data.properties[level].properties).toHaveProperty('side');
      expect(data.properties[level].properties).toHaveProperty('top');
    });
  });

  it('should define a flexible NAVIGATION_MENU_SCHEMA that supports mixed content', () => {
    const response = roleSchema.navigations.response;
    const sideMenu = response['200'].properties.data.properties.root.properties.side;

    expect(sideMenu.type).toBe('array');

    expect(sideMenu.items).toHaveProperty('oneOf');

    const menuItem = sideMenu.items;
    const oneOfOptions = menuItem.oneOf;

    // First level options: string or object
    expect(oneOfOptions[0].type).toBe('string');

    expect(oneOfOptions[1].type).toBe('object');
    expect(oneOfOptions[1].additionalProperties.type).toBe('array');

    // Second level options within the array
    const nestedItems = oneOfOptions[1].additionalProperties.items.oneOf;
    expect(nestedItems[0].type).toBe('string');
    expect(nestedItems[1].type).toBe('object');

    // Check for name/url object structure
    expect(nestedItems[1].properties).toHaveProperty('name');
    expect(nestedItems[1].properties).toHaveProperty('url');
    expect(nestedItems[1].properties.name.type).toBe('string');
    expect(nestedItems[1].properties.url.type).toBe('string');
    expect(nestedItems[1].required).toContain('name');
    expect(nestedItems[1].required).toContain('url');

    // Check for third level nesting option
    expect(nestedItems[2].type).toBe('object');
    expect(nestedItems[2].additionalProperties.type).toBe('array');

    // Check third level items
    const thirdLevelItems = nestedItems[2].additionalProperties.items.oneOf;
    expect(thirdLevelItems[0].type).toBe('string');
    expect(thirdLevelItems[1].type).toBe('object');
    expect(thirdLevelItems[1].properties).toHaveProperty('name');
    expect(thirdLevelItems[1].properties).toHaveProperty('url');
    expect(thirdLevelItems[1].required).toContain('name');
    expect(thirdLevelItems[1].required).toContain('url');
  });
});

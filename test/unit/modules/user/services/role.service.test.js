import {
  ModuleRepository,
  PolicySettingRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { clearCache, generateCacheKey } from '#src/utils/cache.util.js';
import {
  create,
  index,
  navigations,
  options,
  update,
  updateStatus,
  view,
} from '#src/modules/user/services/role.service.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import { RoleValidation } from '#src/modules/core/validations/index.js';
import { getModulePolicyOptions } from '#src/modules/user/services/department.service.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

vi.mock('#src/modules/user/services/policy.service.js', () => ({
  updatePolicySettings: vi.fn(),
  updateDescendantPolicies: vi.fn(),
}));

vi.mock('#src/modules/user/repository/index.js', () => ({
  RoleModuleRepository: {
    findAllByRoleIdWithPolicySettingsAndModules: vi.fn(),
    findOrCreate: vi.fn(),
    update: vi.fn(),
    findAllByRoleIdWithPolicySettings: vi.fn(),
  },
  RoleRepository: {
    findById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    findAll: vi.fn(),
    findByIdWithModulePolicies: vi.fn(),
    findAllByParentPath: vi.fn(),
    findAllByParentId: vi.fn(),
    findByEntityIdAndName: vi.fn(),
  },
  PolicySettingRepository: {
    findOrCreate: vi.fn(),
    update: vi.fn(),
    findByParentId: vi.fn(),
    upsert: vi.fn(),
  },
  ModuleRepository: {
    findAll: vi.fn(),
    findById: vi.fn(),
    findModulePolicies: vi.fn(),
  },
}));

vi.mock('#src/utils/cache.util.js', () => ({
  clearCache: vi.fn(),
  generateCacheKey: vi.fn().mockReturnValue('test-cache-key'),
}));
vi.mock('#src/modules/core/validations/index.js');
vi.mock('#src/modules/user/services/department.service.js');
vi.mock('#src/utils/db-transaction.util.js');

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    MODULE_NAMES: { ROLE: 'ROLE' },
    MODULE_METHODS: {
      INDEX: 'INDEX',
      VIEW: 'VIEW',
      OPTION: 'OPTION',
      NAVIGATION: 'NAVIGATION',
    },
    COMMON_STATUSES: { ACTIVE: 'active', INACTIVE: 'active' },
    REMARK_STATUSES: { ACTIVE: 'active', INACTIVE: 'active' },
  },
}));

vi.mock('#src/modules/user/constants/index.js', () => ({
  RoleConstant: {
    POLICIES: ['canWrite', 'canView', 'canEdit'],
  },
  DepartmentConstant: {
    POLICIES: ['canWrite', 'canView', 'canEdit'],
  },
}));

vi.mock('#src/modules/user/constants/department.constant.js', () => ({
  NAVIGATION_TYPES: {
    SIDE: 'SIDE',
    TOP: 'TOP',
    BOTH: 'BOTH',
  },
}));

describe('Role Service', () => {
  let mockServer;
  let mockRequest;
  let mockTransaction;

  beforeEach(() => {
    mockTransaction = {};
    mockServer = {
      redis: {
        del: vi.fn(),
      },
      psql: {
        connection: {
          transaction: vi.fn().mockImplementation((callback) => callback(mockTransaction)),
          models: {
            Role: {
              findOne: vi.fn(),
              findAll: vi.fn(),
            },
            RoleModulePolicy: {
              findAll: vi.fn(),
              findOrCreate: vi.fn(),
            },
            PolicySetting: {
              findOne: vi.fn(),
              findOrCreate: vi.fn(),
              upsert: vi.fn(),
            },
            Module: {
              findAll: vi.fn(),
              findByPk: vi.fn(),
            },
          },
          Sequelize: {
            Op: {
              ne: Symbol('ne'),
            },
          },
        },
      },
    };

    mockRequest = {
      server: mockServer,
      entity: { id: 'entity-1', hierarchy: 'root' },
      params: { id: 'role-1' },
      query: {},
      body: {},
      authInfo: { id: 'user-1', roleId: 'role-1' },
    };

    clearCache.mockResolvedValue();
    generateCacheKey.mockReturnValue('test-cache-key');
    withTransaction.mockImplementation((_, __, callback) => callback(mockTransaction));
    getModulePolicyOptions.mockResolvedValue([]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('index', () => {
    it('should return all roles for an entity', async () => {
      const mockRoles = [{ id: 'role-1' }, { id: 'role-2' }];
      RoleRepository.findAll.mockResolvedValue(mockRoles);

      const result = await index(mockRequest);

      expect(RoleRepository.findAll).toHaveBeenCalledWith(mockServer, {
        filter_entityId_eq: 'entity-1',
      });
      expect(result).toEqual(mockRoles);
    });
  });

  describe('view', () => {
    it('should return a specific role by ID', async () => {
      const mockRole = { id: 'role-1', name: 'Admin' };
      RoleRepository.findByIdWithModulePolicies.mockResolvedValue(mockRole);

      const result = await view(mockRequest);

      expect(RoleRepository.findByIdWithModulePolicies).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'role-1',
      );
      expect(result).toEqual(mockRole);
    });

    it('should throw an error if role is not found', async () => {
      RoleRepository.findByIdWithModulePolicies.mockResolvedValue(null);
      RoleError.notFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(view(mockRequest)).rejects.toThrow('Role not found');
      expect(RoleError.notFound).toHaveBeenCalledWith('role-1');
    });
  });

  describe('create', () => {
    beforeEach(() => {
      mockRequest.body = {
        name: 'New Role',
        description: 'Role description',
        status: 'active',
        modules: [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }],
      };

      RoleValidation.validateRoleName.mockResolvedValue(true);
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([
        { moduleId: 'module-1', policies: ['canView', 'canWrite'] },
      ]);

      RoleRepository.create.mockResolvedValue({
        id: 'new-role-1',
        name: 'New Role',
        path: 'New Role',
      });

      RoleModuleRepository.findOrCreate.mockResolvedValue([{ id: 'role-module-1' }, true]);

      PolicySettingRepository.findOrCreate.mockResolvedValue([{ id: 'policy-1' }, true]);
    });

    it('should create a new role without parent', async () => {
      const result = await create(mockRequest);

      expect(RoleValidation.validateRoleName).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'New Role',
      );

      expect(RoleRepository.create).toHaveBeenCalledWith(
        mockServer,
        {
          parentId: null,
          entityId: 'entity-1',
          departmentId: undefined,
          name: 'New Role',
          description: 'Role description',
          status: 'active',
          path: 'new_role',
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(clearCache).toHaveBeenCalled();
      expect(result).toEqual({
        id: 'new-role-1',
        name: 'New Role',
        path: 'New Role',
      });
    });

    it('should create a new role with parent', async () => {
      mockRequest.body.parentId = 'parent-1';

      const mockParentRole = {
        id: 'parent-1',
        name: 'Parent Role',
        path: 'Parent Role',
      };

      RoleRepository.findById.mockResolvedValue(mockParentRole);

      const result = await create(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'parent-1');
      expect(RoleRepository.create).toHaveBeenCalledWith(
        mockServer,
        {
          parentId: 'parent-1',
          entityId: 'entity-1',
          departmentId: undefined,
          name: 'New Role',
          description: 'Role description',
          status: 'active',
          path: 'Parent Role.new_role',
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(result).toEqual({
        id: 'new-role-1',
        name: 'New Role',
        path: 'New Role',
      });
    });

    it('should throw an error if parent role is not found', async () => {
      mockRequest.body.parentId = 'non-existent-parent';
      RoleRepository.findById.mockResolvedValue(null);
      RoleError.parentRoleNotFound = vi.fn().mockReturnValue(new Error('Parent role not found'));

      await expect(create(mockRequest)).rejects.toThrow('Parent role not found');
      expect(RoleError.parentRoleNotFound).toHaveBeenCalled();
    });

    it('should call PolicySettingRepository.update when policy already exists', async () => {
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([
        { moduleId: 'module-1', policies: ['canView', 'canWrite'] },
      ]);
      RoleModuleRepository.findOrCreate.mockResolvedValueOnce([{ id: 'role-module-1' }, false]);

      RoleModuleRepository.update.mockResolvedValueOnce({});

      PolicySettingRepository.findOrCreate.mockResolvedValueOnce([
        { id: 'policy-setting-1' },
        false,
      ]);

      PolicySettingRepository.update = vi.fn().mockResolvedValue({ updated: true });

      await create(mockRequest);

      expect(PolicySettingRepository.update).toHaveBeenCalledWith(
        { id: 'policy-setting-1' },
        { canView: true, canWrite: true },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );
    });
  });

  describe('update', () => {
    beforeEach(() => {
      mockRequest.body = {
        name: 'Updated Role',
        description: 'Updated description',
        status: 'active',
        modules: [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }],
        version: 1,
      };

      const mockRole = {
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
        parentId: null,
      };

      RoleRepository.findById.mockResolvedValue(mockRole);
      RoleValidation.validateRoleName.mockResolvedValue(true);
      RoleValidation.validateHierarchy.mockReturnValue(true);
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([
        { moduleId: 'module-1', policies: ['canView', 'canWrite'] },
      ]);

      RoleRepository.update.mockResolvedValue({
        id: 'role-1',
        name: 'Updated Role',
        path: 'Updated Role',
      });

      RoleRepository.findAllByParentPath.mockResolvedValue([]);
      RoleRepository.findAllByParentId.mockResolvedValue([]);
      RoleModuleRepository.findAllByRoleIdWithPolicySettings.mockResolvedValue([
        {
          id: 'existing-module-1',
          moduleId: 'module-1',
          PolicySetting: { id: 'policy-setting-1' },
        },
      ]);

      const mockRoleModule = {
        id: 'role-module-1',
        update: vi.fn().mockResolvedValue({ id: 'role-module-1', updated: true }),
      };
      RoleModuleRepository.findOrCreate.mockResolvedValue([mockRoleModule, false]);

      PolicySettingRepository.findByParentId.mockResolvedValue(null);
      PolicySettingRepository.upsert.mockResolvedValue([{ id: 'policy-setting-1' }, false]);
      PolicySettingRepository.update = vi.fn().mockResolvedValue({ updated: true });
    });

    it('should update an existing role', async () => {
      const result = await update(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'role-1');
      expect(RoleValidation.validateRoleName).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'Updated Role',
        'role-1',
      );

      expect(RoleRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'role-1' }),
        {
          name: 'Updated Role',
          departmentId: undefined,
          description: 'Updated description',
          status: 'active',
          path: 'Updated_Role',
          version: 1,
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(result).toEqual({
        id: 'role-1',
        name: 'Updated Role',
        path: 'Updated Role',
      });
    });

    it('should use original name if name is not provided in request body', async () => {
      delete mockRequest.body.name;

      const mockRole = {
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
        parentId: null,
      };

      RoleRepository.findById.mockResolvedValue(mockRole);

      await update(mockRequest);

      expect(RoleRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'role-1' }),
        {
          name: 'Original Role',
          departmentId: undefined,
          description: 'Updated description',
          status: 'active',
          path: 'Original Role',
          version: 1,
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );
    });

    it('should throw an error if role is not found', async () => {
      RoleRepository.findById.mockResolvedValueOnce(null);
      RoleError.notFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(update(mockRequest)).rejects.toThrow('Role not found');
      expect(RoleError.notFound).toHaveBeenCalledWith('role-1');
    });

    it('should throw an error if user is not authorized to update the role', async () => {
      RoleValidation.validateHierarchy.mockReturnValue(false);
      RoleError.notAuthorizedToUpdate = vi.fn().mockReturnValue(new Error('Not authorized'));

      await expect(update(mockRequest)).rejects.toThrow('Not authorized');
      expect(RoleError.notAuthorizedToUpdate).toHaveBeenCalled();
    });

    it('should update descendant paths when name changes', async () => {
      const descendantRole = {
        id: 'descendant-1',
        path: 'Original Role.Child',
      };

      RoleRepository.findAllByParentPath.mockResolvedValue([descendantRole]);

      await update(mockRequest);

      expect(RoleRepository.findAllByParentPath).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'Original Role',
        { transaction: mockTransaction },
      );

      expect(RoleRepository.update).toHaveBeenCalledWith(
        descendantRole,
        { path: 'Updated_Role.Child' },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );
    });

    it('should throw an error if user role is not found', async () => {
      RoleRepository.findById.mockResolvedValueOnce({
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
      });

      RoleRepository.findById.mockResolvedValueOnce(null);

      RoleError.notFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(update(mockRequest)).rejects.toThrow('Role not found');
      expect(RoleError.notFound).toHaveBeenCalledWith('role-1');

      expect(RoleRepository.findById).toHaveBeenNthCalledWith(1, mockServer, 'role-1');

      expect(RoleRepository.findById).toHaveBeenNthCalledWith(2, mockServer, 'role-1');
    });

    it('should update descendant role policies correctly', async () => {
      const descendantRole = {
        id: 'descendant-1',
        path: 'Original Role.Child',
      };

      RoleRepository.findAllByParentId.mockResolvedValueOnce([descendantRole]);

      const descendantRoleModule = { id: 'descendant-role-module-1' };
      RoleModuleRepository.findOrCreate.mockResolvedValueOnce([descendantRoleModule, true]);

      const descendantPolicySettings = {
        id: 'descendant-policy-1',
        canView: true,
        canWrite: false,
        canEdit: true,
        toJSON: () => ({
          id: 'descendant-policy-1',
          canView: true,
          canWrite: false,
          canEdit: true,
        }),
      };
      PolicySettingRepository.findByParentId.mockResolvedValueOnce(descendantPolicySettings);

      RoleRepository.findAllByParentId.mockResolvedValueOnce([]);

      await update(mockRequest);

      expect(RoleRepository.findAllByParentId).toHaveBeenCalledWith(mockServer, 'role-1');

      expect(RoleModuleRepository.findOrCreate).toHaveBeenCalledWith(
        mockServer,
        { roleId: 'role-1', moduleId: 'module-1' },
        {},
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(PolicySettingRepository.findByParentId).toHaveBeenCalledWith(
        mockServer,
        'role-module-1',
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(PolicySettingRepository.upsert).toHaveBeenCalledWith(
        mockServer,
        { canView: true, canWrite: true, canEdit: false, parentId: 'descendant-role-module-1' },
        { where: { parentId: 'descendant-role-module-1' } },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(RoleRepository.findAllByParentId).toHaveBeenCalledWith(mockServer, 'descendant-1');
    });
    it('should set policies to false for modules not in the request', async () => {
      mockRequest.body.modules = [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }];

      const existingPolicies = [
        {
          id: 'role-module-2',
          moduleId: 'module-2',
          policySetting: { id: 'policy-setting-2' },
        },
      ];

      RoleModuleRepository.findAllByRoleIdWithPolicySettings.mockResolvedValue(existingPolicies);

      PolicySettingRepository.upsert = vi.fn().mockResolvedValue({});
      RoleRepository.findAllByParentId = vi.fn().mockResolvedValue([]);

      await update(mockRequest);

      expect(PolicySettingRepository.upsert).toHaveBeenCalledWith(
        mockServer,
        {
          parentId: 'role-module-2',
          canView: false,
          canWrite: false,
          canEdit: false,
        },
        { where: { parentId: 'role-module-2' } },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(RoleRepository.findAllByParentId).toHaveBeenCalledWith(mockServer, 'role-1');
    });
  });

  describe('updateStatus', () => {
    beforeEach(() => {
      mockRequest.body = { status: 'inactive' };

      const mockRole = {
        id: 'role-1',
        name: 'Test Role',
        status: 'active',
      };

      RoleRepository.findById.mockResolvedValue(mockRole);
      RoleRepository.update.mockResolvedValue({
        id: 'role-1',
        name: 'Test Role',
        status: 'inactive',
      });
    });

    it('should update the status of a role', async () => {
      const result = await updateStatus(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'role-1');
      expect(RoleRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'role-1' }),
        { status: 'inactive' },
        mockRequest.user,
      );

      expect(result).toEqual({
        id: 'role-1',
        name: 'Test Role',
        status: 'inactive',
      });
    });

    it('should throw an error if role is not found', async () => {
      RoleRepository.findById.mockResolvedValue(null);
      RoleError.notFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(updateStatus(mockRequest)).rejects.toThrow('Role not found');
      expect(RoleError.notFound).toHaveBeenCalledWith('role-1');
    });
  });

  describe('options', () => {
    it('should return module policy options', async () => {
      const mockOptions = [
        { id: 'option-1', name: 'Option 1' },
        { id: 'option-2', name: 'Option 2' },
      ];

      getModulePolicyOptions.mockResolvedValue(mockOptions);

      const result = await options(mockRequest);

      expect(getModulePolicyOptions).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual(mockOptions);
    });
  });

  describe('navigations', () => {
    beforeEach(() => {
      mockRequest = {
        server: mockServer,
        entity: { id: 'entity-1' },
        authInfo: { roleId: 'role-123' },
      };

      RoleRepository.findById.mockResolvedValue({
        id: 'role-123',
        name: 'Test Role',
      });

      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'module-1',
          name: 'Dashboard',
          navigationUrl: '/dashboard',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'module-2',
          name: 'Users',
          navigationUrl: '/users',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'module-3',
          name: 'User List',
          navigationUrl: '/userlist',
          parentId: 'module-2',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'module-4',
          name: 'Settings',
          navigationUrl: '/settings',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'TOP',
        },
        {
          id: 'module-5',
          name: 'Reports',
          navigationUrl: '/reports',
          parentId: null,
          hierarchy: 'merchant',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicySettingsAndModules.mockResolvedValue([
        {
          moduleId: 'module-1',
          policySetting: { canView: true, canEdit: true },
        },
        {
          moduleId: 'module-2',
          policySetting: { canView: true, canEdit: false },
        },
        {
          moduleId: 'module-3',
          policySetting: { canView: true, canEdit: false },
        },
        {
          moduleId: 'module-4',
          policySetting: { canView: true, canEdit: true },
        },
        {
          moduleId: 'module-5',
          policySetting: { canView: false, canEdit: false },
        },
      ]);
    });

    it('should return menu structure based on role permissions', async () => {
      const result = await navigations(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'role-123');

      expect(RoleModuleRepository.findAllByRoleIdWithPolicySettingsAndModules).toHaveBeenCalledWith(
        mockServer,
        'role-123',
      );

      expect(result).toHaveProperty('root');
      expect(result).toHaveProperty('merchant');

      expect(result.root).toHaveProperty('side');
      expect(result.root).toHaveProperty('top');

      expect(result.root.side).toContainEqual(
        expect.objectContaining({
          name: 'Dashboard',
          url: '/dashboard',
        }),
      );

      expect(result.root.side).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'Dashboard',
            url: '/dashboard',
          }),
          {
            Users: [
              expect.objectContaining({
                name: 'User List',
                url: '/userlist',
              }),
            ],
          },
        ]),
      );

      expect(result.root.side).toContainEqual({
        Users: [
          expect.objectContaining({
            name: 'User List',
            url: '/userlist',
          }),
        ],
      });
    });

    it('should throw an error if user role is not found', async () => {
      RoleRepository.findById.mockResolvedValue(null);

      await expect(navigations(mockRequest)).rejects.toThrow();
    });

    it('should handle empty module policies', async () => {
      RoleModuleRepository.findAllByRoleIdWithPolicySettingsAndModules.mockResolvedValue([]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toEqual([]);
      expect(result.root.top).toEqual([]);
      expect(result.merchant.side).toEqual([]);
      expect(result.merchant.top).toEqual([]);
    });

    it('should handle modules with mixed navigation types', async () => {
      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'module-1',
          name: 'Dashboard',
          navigationUrl: '/dashboard',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'BOTH',
        },
        {
          id: 'module-2',
          name: 'Users',
          navigationUrl: '/users',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicySettingsAndModules.mockResolvedValue([
        {
          moduleId: 'module-1',
          policySetting: { canView: true, canEdit: true },
        },
        {
          moduleId: 'module-2',
          policySetting: { canView: true, canEdit: false },
        },
      ]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toContainEqual(
        expect.objectContaining({
          name: 'Dashboard',
          url: '/dashboard',
        }),
      );
      expect(result.root.top).toContainEqual(
        expect.objectContaining({
          name: 'Dashboard',
          url: '/dashboard',
        }),
      );

      expect(result.root.side).toContainEqual(
        expect.objectContaining({
          name: 'Users',
          url: '/users',
        }),
      );
      expect(result.root.top).not.toContainEqual(
        expect.objectContaining({
          name: 'Users',
          url: '/users',
        }),
      );
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle database transaction rollback on error', async () => {
      mockRequest.body = {
        departmentId: 'dept-1',
        name: 'Test Role',
        modules: [],
      };

      RoleRepository.create.mockRejectedValue(new Error('Database error'));
      mockServer.psql.connection.transaction.mockImplementation(async (callback) => {
        try {
          await callback(mockTransaction);
        } catch (error) {
          await mockTransaction.rollback();
          throw error;
        }
      });

      await expect(create(mockRequest)).rejects.toThrow('Database error');
    });
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
// import { formatErrorResponse } from '#src/utils/response.util.js';
import { translateValidationErrors } from '#src/utils/i18next.util.js';

vi.mock('#src/utils/i18next.util.js', () => ({
  translateValidationErrors: vi.fn(),
}));

vi.mock('fastify-plugin', () => ({
  __esModule: true,
  default: (fn) => fn,
}));

// Import the plugin after mocking dependencies
const errorHandlerPlugin = (await import('#src/plugins/error-handler.plugin.js')).default;

describe('Error Handler Plugin', () => {
  let mockFastify;
  let mockReply;
  const mockRequest = {};

  beforeEach(() => {
    vi.clearAllMocks();

    mockFastify = {
      setNotFoundHandler: vi.fn(),
      setErrorHandler: vi.fn(),
      t: vi.fn().mockImplementation((key) => `t_${key}`),
      config: {
        NODE_ENV: 'production',
      },
    };

    mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };
  });

  it('should set up not found handler', async () => {
    await errorHandlerPlugin(mockFastify);

    expect(mockFastify.setNotFoundHandler).toHaveBeenCalled();

    const notFoundHandler = mockFastify.setNotFoundHandler.mock.calls[0][0];
    await notFoundHandler(mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 't_error.sentence.itemNotFound',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: {},
    });
  });

  it('should set up error handler', async () => {
    await errorHandlerPlugin(mockFastify);

    expect(mockFastify.setErrorHandler).toHaveBeenCalled();

    const mockError = new Error('Test error');
    mockError.statusCode = 400;
    mockError.code = 'BAD_REQUEST';
    mockError.metaData = { foo: 'bar' };

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 'Test error',
      errorCode: 'BAD_REQUEST',
      meta: { foo: 'bar' },
    });
  });

  it('should handle errors without statusCode', async () => {
    await errorHandlerPlugin(mockFastify);

    const mockError = new Error('Internal error');

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 't_error.sentence.internal',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: {},
    });
  });

  it('should reveal real 500 error for development env', async () => {
    mockFastify.config.NODE_ENV = 'development';
    await errorHandlerPlugin(mockFastify);

    const realErrorMessage = 'Real error';
    const mockError = new Error(realErrorMessage);
    const mockFormattedResponse = {
      message: realErrorMessage,
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: {},
    };

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
  });

  it('should translate custom error message with tMessageFn function', async () => {
    await errorHandlerPlugin(mockFastify);

    const customErrorMessage = 'Custom error';
    const translatedMessage = `t_${customErrorMessage}`;

    const mockError = new Error(customErrorMessage);
    mockError.statusCode = 400;
    mockError.tMessageFn = vi.fn().mockReturnValue(translatedMessage);
    const mockFormattedResponse = {
      message: translatedMessage,
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: {},
    };

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
  });

  it('should handle validation errors', async () => {
    await errorHandlerPlugin(mockFastify);

    const mockError = new Error('Validation Error');
    mockError.validation = [{ message: 'Invalid input' }];

    mockFastify.t.mockReturnValue('Validation Error');
    translateValidationErrors.mockReturnValue(['Translated error']);

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(translateValidationErrors).toHaveBeenCalledWith([{ message: 'Invalid input' }]);
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 'Validation Error',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: { details: ['Translated error'] },
    });
  });
});

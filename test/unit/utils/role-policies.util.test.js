import { beforeEach, describe, expect, it, vi } from 'vitest';
import { checkPolicies } from '#src/utils/role-policies.util.js';
import { findByIdWithModulePolicies } from '#src/modules/user/repository/role.repository.js';
import { roleError } from '#src/modules/user/errors/role.error.js';

vi.mock('#src/modules/user/repository/role.repository.js', () => ({
  findByIdWithModulePolicies: vi.fn(),
}));

vi.mock('#src/modules/user/errors/role.error.js', () => ({
  roleError: {
    unauthorizedAccess: vi.fn().mockReturnValue(new Error('Unauthorized access')),
    notFound: vi.fn().mockReturnValue(new Error('Role not found')),
  },
}));

vi.mock('#src/modules/core/constants/core.constant.js', () => ({
  ACCESS_LEVEL_KEYS: {
    DEFAULT: 'default',
    ROOT: 'root',
    MERCHANT: 'merchant',
  },
}));

describe('Role Policies Utility', () => {
  let mockRequest;
  let mockFastify;
  let mockRouteConfig;

  beforeEach(() => {
    vi.resetAllMocks();

    mockRequest = {
      entity: {
        id: 'entity-1',
        hierarchy: 'root',
      },
      authInfo: {
        roleId: 'role-1',
      },
      userEntity: {
        id: 'entity-2',
        hierarchy: 'root',
      },
    };

    mockFastify = {
      log: {
        error: vi.fn(),
      },
    };

    mockRouteConfig = {
      policy: 'user.canView',
    };
  });

  it('should return early if no policy is required', async () => {
    await checkPolicies(mockRequest, mockFastify, {});
    expect(findByIdWithModulePolicies).not.toHaveBeenCalled();
  });

  it('should throw error if role is not found', async () => {
    findByIdWithModulePolicies.mockResolvedValue(null);

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );

    expect(findByIdWithModulePolicies).toHaveBeenCalledWith(mockFastify, 'entity-2', 'role-1');

    expect(roleError.unauthorizedAccess).toHaveBeenCalled();
  });

  it('should throw error if hierarchy modules are not found', async () => {
    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );

    expect(roleError.unauthorizedAccess).toHaveBeenCalled();
  });

  it('should throw error if module policy is not found', async () => {
    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        root: [
          {
            id: 'module-1',
            name: 'dashboard',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );

    expect(roleError.unauthorizedAccess).toHaveBeenCalled();
  });

  it('should throw error if policy action is not allowed', async () => {
    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        root: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canEdit: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );

    expect(roleError.unauthorizedAccess).toHaveBeenCalled();
  });

  it('should pass if policy action is allowed', async () => {
    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        root: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
              canEdit: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });

  it('should allow access for root users to any entity', async () => {
    mockRequest.userEntity = { id: 'root-1', hierarchy: 'root' };
    mockRequest.entity = { id: 'merchant-1', hierarchy: 'merchant' };
    mockRequest.parentEntity = { id: 'org-1', hierarchy: 'organisation' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });

  it('should allow organisation users to access their own organisation', async () => {
    const orgId = 'org-1';
    mockRequest.userEntity = { id: orgId, hierarchy: 'organisation' };
    mockRequest.entity = { id: orgId, hierarchy: 'organisation' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        organisation: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });

  it('should allow organisation users to access merchants under their organisation', async () => {
    const orgId = 'org-1';
    const merchantId = 'merchant-1';
    mockRequest.userEntity = { id: orgId, hierarchy: 'organisation' };
    mockRequest.entity = { id: merchantId, hierarchy: 'merchant' };
    mockRequest.parentEntity = { id: orgId, hierarchy: 'organisation' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });

  it('should deny organisation users access to other organisations', async () => {
    const orgId = 'org-1';
    const otherOrgId = 'org-2';
    mockRequest.userEntity = { id: orgId, hierarchy: 'organisation' };
    mockRequest.entity = { id: otherOrgId, hierarchy: 'organisation' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        organisation: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );
  });

  it('should deny organisation users access to merchants under other organisations', async () => {
    const orgId = 'org-1';
    const otherOrgId = 'org-2';
    const merchantId = 'merchant-1';
    mockRequest.userEntity = { id: orgId, hierarchy: 'organisation' };
    mockRequest.entity = { id: merchantId, hierarchy: 'merchant' };
    mockRequest.parentEntity = { id: otherOrgId, hierarchy: 'organisation' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );
  });

  it('should allow merchant users to access their own merchant', async () => {
    const merchantId = 'merchant-1';
    mockRequest.userEntity = { id: merchantId, hierarchy: 'merchant' };
    mockRequest.entity = { id: merchantId, hierarchy: 'merchant' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });

  it('should deny merchant users access to other merchants', async () => {
    const merchantId = 'merchant-1';
    const otherMerchantId = 'merchant-2';
    mockRequest.userEntity = { id: merchantId, hierarchy: 'merchant' };
    mockRequest.entity = { id: otherMerchantId, hierarchy: 'merchant' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );
  });

  it('should be case insensitive for module names', async () => {
    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        root: [
          {
            id: 'module-1',
            name: 'USER',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });

  it('should deny access when user hierarchy is unknown', async () => {
    mockRequest.userEntity = { id: 'user-1', hierarchy: 'unknown' };
    mockRequest.entity = { id: 'entity-1', hierarchy: 'merchant' };
    mockRequest.parentEntity = { id: 'org-1', hierarchy: 'organisation' };

    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        merchant: [
          {
            id: 'module-1',
            name: 'user',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
      'Unauthorized access',
    );
  });

  it('should be case insensitive for module names', async () => {
    findByIdWithModulePolicies.mockResolvedValue({
      id: 'role-1',
      name: 'Admin Role',
      modulePolicies: {
        root: [
          {
            id: 'module-1',
            name: 'USER',
            policySettings: {
              canView: true,
            },
          },
        ],
      },
    });

    await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
  });
});
